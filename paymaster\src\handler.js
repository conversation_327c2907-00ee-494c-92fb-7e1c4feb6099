// define graphql handler
module.exports.graphql = (event, context, callback) => {

    // require apollo-server-lambda to define lamda functions
    var { ApolloServer, gql } = require('apollo-server-lambda');
    // require resolver
    var { resolvers } = require('./resolver');
    // require fs
    const fs = require('fs');
    // require schema.graphql
    const typeDefs = gql(fs.readFileSync(__dirname.concat('/schema.graphql'), 'utf8'));
    // require common hrapp-corelib functions
    const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

    //to send the response immediately when callback executes
    context.callbackWaitsForEmptyEventLoop = false;    

    // get customAuthorizerData from firebase authorize and  return it to resolver if exists.
    let idToken = event.requestContext.authorizer.idToken ? event.requestContext.authorizer.idToken : '';
    let refreshToken = event.requestContext.authorizer.refreshToken ? event.requestContext.authorizer.refreshToken : '';

    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            // get token from headers
            let authToken = (event.headers.Authorization && event.headers.Authorization !== 'null' && event.headers.Authorization !== 'undefined') ? event.headers.Authorization : idToken;

            let authDetails ={
                idToken: authToken,
                refreshToken: refreshToken,
                partnerid: event.headers.partnerid ? event.headers.partnerid : event.headers.Partnerid ?  event.headers.Partnerid : '-'
            };
            let contextData = await commonLib.func.getContextDataWithEmployeeId(event,authDetails,'wo');

            // close dbconnection
            return {
                orgCode:contextData.Org_Code ,
                logInEmpId:contextData.Employee_Id,
                connection:contextData.connection,
                userIp:contextData.User_Ip,
                partnerid:  contextData.partnerid
            };
        }
    });

    // create handler object
    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });

    // After executing the resolver function the response will be caught here (either error or succes) and 
    // it will be return to UI.
    function callbackFilter(error, output) {

        // We are appending the idToken and refreshToken in the response. While running this in local this is not returning the response
        // so here checked the stagename as local or not. If it is local then we will no append the token response. 
        // Otherwise token response will be append and response will be returned
        // If any doubts check this task #3794
        if (process.env.stageName !== 'local') {
            // parse the response data
            let responseData = JSON.parse(output.body);
            // push idToken and refresh token into an json object
            let identityToken = {
                idToken: idToken,
                refreshToken: refreshToken
            }
            // return the idToken and refreshTOken to UI
            responseData.identityToken = identityToken;
            output.body = JSON.stringify(responseData);
        }
        
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }

    // send event, contect and callbackFilter as params to the resolver function
    return handler(event, context, callbackFilter);
};
