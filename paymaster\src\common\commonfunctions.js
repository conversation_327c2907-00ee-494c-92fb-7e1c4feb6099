// Common workflow functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ehrTables } = require('./tablealias');
const axios = require('axios');
const { validateWithRules } = require('@cksiva09/validationlib/src/validator');
const { evaluateFormula } = require('./formulaEvaluation');
const { formId } = require('./appconstants');
const moment = require('moment-timezone');

/**
 * Get workflow process instance ID for salary revision
 * @param {Object} organizationDbConnection - Database connectionpp
 * @param {number} uniqueId - Unique ID (Revision_Id)
 * @returns {Promise<Array>} - Process instance data
 */
async function getWorkflowProcessInstanceId(organizationDbConnection, uniqueId) {
  try {
    return await organizationDbConnection(ehrTables.salaryRevisionDetails + ' as SRD')
      .select(
        'SRD.Process_Instance_Id',
        'SRD.Added_On',
        'SRD.Added_By',
        organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as Added_By_Name")
      )
      .leftJoin(`${ehrTables.empPersonalInfo} as EPI3`, 'SRD.Employee_Id', 'EPI3.Employee_Id')
      .where('Revision_Id', uniqueId);
  } catch (err) {
    console.log('Error in getWorkflowProcessInstanceId', err);
    throw err;
  }
}
async function getOneTimeEarningWorkflowProcessInstanceId(organizationDbConnection, uniqueId) {
  try {
    return await organizationDbConnection(`${ehrTables.employeeOneTimeEarnings} as EOTE`)
      .select(
        'EOTE.Process_Instance_Id',
        'EOTE.Added_On',
        'EOTE.Added_By',
        organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as Added_By_Name")
      )
      .leftJoin(`${ehrTables.empPersonalInfo} as EPI3`, 'EOTE.Employee_Id', 'EPI3.Employee_Id')
      .where('EOTE.One_Time_Earning_Id', uniqueId);
  } catch (err) {
    console.log('Error in getOneTimeEarningWorkflowProcessInstanceId', err);
    throw err;
  }
}
async function validateCommonRuleInput(args, fieldValidations) {
  try {
    let validationError = {};
    for (const field in fieldValidations) {
      let fieldName = field; // By default, use the current field name
      if (args.hasOwnProperty(field)) {
        const validation = validateWithRules(args[field], fieldName);
        if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {

          validationError[fieldValidations[field]] = validation.length ? commonLib.func.getError('', fieldValidations[field]).message : commonLib.func.getError('', fieldValidations[field]).message1;
        }
      }
    }
    return validationError;
  }
  catch (err) {
    console.log('Error in the validateCommonRuleInput function in the main catch block.', err);
    throw err;
  }
}
async function validateCandidateStatus(organizationDbConnection, candidateId, operation = 'update', partnerId = null) {
  try {
    const candidateStatus = await organizationDbConnection(ehrTables.candidateRecruitmentInfo + " as CRI")
      .select('CRI.Candidate_Status', 'AST.Status')
      .leftJoin('ats_status_table as AST', 'AST.Id', 'CRI.Candidate_Status')
      .where('CRI.Candidate_Id', candidateId)
      .first();

    // Check if candidate exists
    if (!candidateStatus) {
      throw 'IVE0528'; // Invalid candidate ID
    }

    // Different validation rules for update vs delete operations
    if (operation === 'delete') {
      // For DELETE: Only allow "Hired with compensation" status
      if (candidateStatus.Status !== 'Hired with compensation') {
        throw 'IVE0720'; // Cannot delete salary - only allowed for "Hired with compensation" status
      }
    } else {
        let allowedStatuses = [];
        if (partnerId?.toLowerCase() === 'canvas') {
            allowedStatuses = [
                'Offer letter Accepted',
                'Offer Negotiation'
            ];
        } else {
            allowedStatuses = [
                'Offer letter Declined',
                'Offer Letter Rejected (Manager/Admin)',
                'Hired with compensation'
            ];
        }

        if (!allowedStatuses.includes(candidateStatus.Status)) {
            throw 'IVE0720'; // Cannot modify salary - status not allowed
        }
    }
  } catch (error) {
    console.error('Error in validateCandidateStatus:', error);
    throw error;
  }
}

async function revertCandidateStatusOnDelete(organizationDbConnection, candidateIds, trx) {
  try {
    // Get "Hired" status ID
    const hiredStatusRecord = await organizationDbConnection('ats_status_table')
      .select('Id')
      .where('Status', 'Hired')
      .where('Form_Id', 16)
      .first();

    if (!hiredStatusRecord) {
      return;
    }

    // Get "Hired with compensation" status ID
    const hiredWithCompensationRecord = await organizationDbConnection('ats_status_table')
      .select('Id')
      .where('Status', 'Hired with compensation')
      .where('Form_Id', 16)
      .first();

    if (!hiredWithCompensationRecord) {
    console.error('Hired status not found in ats_status_table');
      return;
    }

    // Revert candidates with "Hired with compensation" status back to "Hired"
    await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
      .whereIn('Candidate_Id', candidateIds)
      .where('Candidate_Status', hiredWithCompensationRecord.Id)
      .update({
        Candidate_Status: hiredStatusRecord.Id
      })
      .transacting(trx);

  } catch (error) {
    console.error('Error in revertCandidateStatusOnDelete:', error);
    throw error;
  }
}
async function getFiscalMonthYear(organizationDbConnection, orgCode, assessmentYear) {
    try {
        // Validate orgCode is provided
        if (!orgCode) {
            console.log('Error in getFiscalMonthYear: orgCode is required');
            return [];
        }

        // Validate assessmentYear is a finite integer
        const parsedAssessmentYear = parseInt(assessmentYear, 10);

        // Scope query by Org_Code to prevent cross-tenant data reads
        const orgDetails = await organizationDbConnection(ehrTables.orgDetails)
            .select('Fiscal_StartMonth')
            .where('Org_Code', orgCode)
            .first();

        // Normalize and validate Fiscal_StartMonth
        let fiscalStartMonth = parseInt(orgDetails?.Fiscal_StartMonth, 10);
        if (!Number.isInteger(fiscalStartMonth) || fiscalStartMonth < 1 || fiscalStartMonth > 12) {
            fiscalStartMonth = 4; // sane default (April)
        }

        let tdsMonthArray = [];
        let financialStartYear, financialEndYear;

        financialStartYear = parsedAssessmentYear - 1;
        financialEndYear = parsedAssessmentYear;


        for (let month = fiscalStartMonth; month <= 12; month++) {
            tdsMonthArray.push(`${month},${financialStartYear}`);
        }
        for (let month = 1; month < fiscalStartMonth; month++) {
            tdsMonthArray.push(`${month},${financialEndYear}`);
        }

        return tdsMonthArray;
    } catch (error) {
        console.log('Error in getFiscalMonthYear:', error);
        return [];
    }
}
async function getFiscalStartDate(orgCode, organizationDbConnection) {
  try {
      // Get organization details (equivalent to $this->_orgDetails in PHP)
      const orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 0);

      if (orgDetails && Object.keys(orgDetails).length > 0) {
          // Get the current assessment year from the org details table (equivalent to $this->getAssessmentYr())
          const assessmentYear = orgDetails.Assessment_Year;

          if (assessmentYear) {
              // Financial start year (equivalent to $assessmentYear - 1 in PHP)
              const financialStartYear = assessmentYear - 1;

              // Get fiscal start and end month from org details (equivalent to $this->getFiscalMonth() in PHP)
              const fiscalStartMonth = orgDetails.Fiscal_StartMonth;
              let fiscalEndMonth;

              if (fiscalStartMonth) {
                  if (fiscalStartMonth > 1 && fiscalStartMonth <= 12) {
                      fiscalEndMonth = fiscalStartMonth - 1;
                  } else {
                      fiscalEndMonth = 12;
                  }
                  // Run getSalaryDay calls in parallel for better performance
                  const [financialClosureStartDates] = await Promise.all([
                      // Get the financial start month paycycle dates (equivalent to $dbPayslip->getSalaryDay($start, $assessmentYear-1))
                      commonLib.func.getSalaryDay(
                          orgCode,
                          organizationDbConnection,
                          fiscalStartMonth,
                          assessmentYear - 1
                      ),
                      // Get the financial end month paycycle dates (equivalent to $dbPayslip->getSalaryDay($end, $assessmentYear))
                      commonLib.func.getSalaryDay(
                          orgCode,
                          organizationDbConnection,
                          fiscalEndMonth,
                          assessmentYear
                      )
                  ]);

                  // Set fiscal start date (equivalent to $fiscalFromdate = $financialClosureStartDates['Salary_Date'])
                  return financialClosureStartDates.Salary_Date;
              }
          }
      }
  } catch (fiscalError) {
    console.log('Error getting fiscal start date:', fiscalError);
  }

  return null;
}
async function checkSalaryMigrationAllowed(organizationDbConnection, trx = null) {
  try {

    const query = organizationDbConnection(ehrTables.payrollGeneralSettings)
      .select('Enable_Salary_Template')
      .first();

    if (trx) {
      query.transacting(trx);
    }

    const result = await query;
    const enableSalaryTemplate = result?.Enable_Salary_Template === 1;
    return enableSalaryTemplate;
  } catch (error) {
    // Default to false in case of error
    return false;
  }
}

/**
 * Initiate workflow
 * @param {string} eventId - Event ID
 * @param {Object} instanceData - Instance data
 * @param {string} orgCode - Organization code
 * @param {number} formId - Form ID
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} loginEmployeeId - Login employee ID
 * @param {Object} trx - Transaction object
 * @returns {Promise<void>}
 */
async function initaiteWorkflow(eventId, instanceData, orgCode, formId, organizationDbConnection, loginEmployeeId, trx) {
  try {
    let response = await commonLib.func.initiateWorkflow(eventId, instanceData, orgCode, formId, loginEmployeeId);
    if (response && response.status === 200 && response.data?.workflowProcessInstanceId) {
      await updateWorkflowProcessInstanceId(organizationDbConnection, instanceData.id, response.data.workflowProcessInstanceId, trx);
    } else {
      throw 'ATS0009';
    }
  } catch (mainError) {
    console.log('Error in initiateWorkflow catch block', mainError);
    throw 'ATS0009';
  }
}

async function deleteOldApprovalRecordsWithoutTrx(organizationDbConnection, processInstanceId) {
  try {
    await organizationDbConnection(ehrTables.taProcessInstanceHistory)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    await organizationDbConnection(ehrTables.taProcessInstance)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    await organizationDbConnection(ehrTables.taUserTask)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    await organizationDbConnection(ehrTables.taUserTaskHistory)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    return true;
  } catch (err) {
    console.log('Error in deleteOldApprovalRecordsWithoutTrx catch block', err);
    throw 'CHR0052';
  }
}
/**
 * Update workflow process instance ID for salary revision
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} uniqueId - Unique ID (Revision_Id)
 * @param {string} workflowProcessInstanceId - Workflow process instance ID
 * @param {Object} trx - Transaction object
 * @returns {Promise<void>}
 */
async function updateWorkflowProcessInstanceId(organizationDbConnection, uniqueId, workflowProcessInstanceId, trx) {
  try {
    let query = organizationDbConnection(ehrTables.salaryRevisionDetails)
      .where('Revision_Id', uniqueId);

    // Only use transaction if provided
    if (trx) {
      query = query.transacting(trx);
    }

    return await query.update({ Process_Instance_Id: workflowProcessInstanceId });
  } catch (err) {
    console.log('Error in updateWorkflowProcessInstanceId', err);
    throw err;
  }
}

/**
 * Get event ID for a form
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} formId - Form ID
 * @returns {Promise<string>} - Event ID
 */
async function getEventId(organizationDbConnection, formId) {
  try {
    return (
      await organizationDbConnection(ehrTables.workflows)
        .pluck("WF.Event_Id")
        .where('WFM.Form_Id', formId)
        .innerJoin(ehrTables.workflowModule + ' as WFM', 'WFM.Workflow_Module_Id', 'WF.Workflow_Module_Id ')
        .from(ehrTables.workflows + ' as WF')
        .andWhere("WF.Default_Workflow", 1)
        .then((workflowId) => {
          let val = workflowId[0];
          if (workflowId) return val;
          else return '';
        })
        .catch((catchError) => {
          throw catchError;
        })
    );
  } catch (err) {
    console.log('Error in getEventId', err);
    throw err;
  }
}

/**
 * Retrieves round off settings from the database
 * @param {string} roundOffFor - The type of value to get settings for
 * @returns {Promise<Object>} - The round off settings
 */
async function getRoundOffSettings(organizationDbConnection) {
  try {
    const roundOffSettings = await organizationDbConnection(ehrTables.payrollRoundOffSettings)
      .select('Round_Off_Settings_Id', 'Multiples_Of', 'Round_Off_For', 'Form_Id')
    return roundOffSettings || [];
  } catch (error) {
    throw error;
  }
}

/**
 * Rounds off a value based on the specified rounding settings
 * @param {string} roundOffFor - The type of value to round off (e.g., 'EPF', 'ESI', etc.)
 * @param {number} value - The value to be rounded
 * @param {Object} [roundOffSettings=null] - Optional round off settings object
 * @param {number} [semiValue=2] - Multiplier for decimal rounding (default: 2)
 * @returns {Promise<number>} - The rounded value
 */
function getRoundOffValue(roundOffFor, value, roundOffSettings, semiValue = 2) {
  try {
    // Handle undefined or null roundOffSettings
    if (!roundOffSettings || !Array.isArray(roundOffSettings)) {
      return value;
    }

    // Handle NaN values
    if (isNaN(value) || value === null || value === undefined) {
      return null; // Return null instead of NaN
    }

    let roundOffSetting = roundOffSettings.find((item) => item.Form_Id == roundOffFor);
    if (roundOffSetting) {
      const multiplesOf = parseFloat(roundOffSetting.Multiples_Of) || 0;
      const roundOffSettingId = parseInt(roundOffSetting.Round_Off_Settings_Id) || 0;

      if (multiplesOf === 0.5) {
        switch (roundOffSettingId) {
          case 1: // Round to nearest 0.5 or 1
            return Math.round(value * semiValue) / semiValue;
          case 2: // Round up to next 0.5 or 1
            return Math.ceil(value * semiValue) / semiValue;
          case 3: // Round down to previous 0.5 or 1
            return Math.floor(value * semiValue) / semiValue;
          default: // No rounding
            return parseFloat(Number(value).toFixed(2))
        }
      } else {
        switch (roundOffSettingId) {
          case 1: // Round to nearest integer
            return Math.round(value);
          case 2: // Round up to next integer
            return Math.ceil(value);
          case 3: // Round down to previous integer
            return Math.floor(value);
          default: // No rounding
            return parseFloat(Number(value).toFixed(2));
        }
      }
    }

    return value;
  } catch (error) {
    throw error;
  }
}

// ===== FORMULA-BASED SALARY CALCULATION FUNCTIONS =====

/**
 * Merges input allowance payload with database formulas and configuration
 * @param {Array} inputAllowanceData - Allowance data from API payload
 * @param {Object} organizationDbConnection - Database connection
 * @returns {Array} - Merged allowance details with database formulas
 */
async function mergeAllowanceDetailsWithDatabase(inputAllowanceData, organizationDbConnection) {
    try {
        if (!inputAllowanceData || inputAllowanceData.length === 0) {
            return [];
        }

        // Extract allowance type IDs from input
        const allowanceTypeIds = inputAllowanceData.map(item => item.Allowance_Type_Id);

        // Fetch allowance type details from database
        const { ehrTables } = require('./tablealias');

        const databaseAllowanceDetails = await organizationDbConnection(ehrTables.allowanceType + ' as AT')
            .select([
                'AT.Allowance_Type_Id',
                'AT.Allowance_Name',
                'AT.Calculation_Type',
                'AT.Custom_Formula',
                'AT.Allowance_Percentage as Percentage', 'AT.Allowance_Amount as Amount',
                'AT.Allowance_Mode',
                'AT.Period',
                'AT.Is_Basic_Pay',
                'AT.Formula_Based',
                'AT.Consider_For_EPF_Contribution',
                'AT.Salary_Component_Id', // CRITICAL: Include this for dynamic mapping
                'SC.Component_Code as Salary_Component_Code',
                organizationDbConnection.raw('GROUP_CONCAT(BF.Form_Id) as BenefitForms')
            ])
            .leftJoin('salary_components as SC', 'SC.Component_Id', 'AT.Salary_Component_Id')
            .leftJoin('allowance_type_benefit_association as ABA', 'ABA.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .leftJoin('benefit_forms as BF', 'BF.Form_Id', 'ABA.Form_Id')
            .whereIn('AT.Allowance_Type_Id', allowanceTypeIds)
            .where('AT.AllowanceType_Status', 'Active')
            .groupBy('AT.Allowance_Type_Id');


        const mergedDetails = inputAllowanceData.map(inputItem => {
            const dbItem = databaseAllowanceDetails.find(db => db.Allowance_Type_Id === inputItem.Allowance_Type_Id);

            if (!dbItem) {
                return inputItem;
            }
            const merged = {
                ...dbItem,
                Percentage:inputItem.Percentage || 0,
                FBP_Max_Declaration:inputItem.FBP_Max_Declaration || dbItem.FBP_Max_Declaration,
                Amount:inputItem.Amount || 0,
                Custom_Formula: dbItem.Custom_Formula,
                Component_Code: dbItem.Salary_Component_Code ||
                               (dbItem.Allowance_Name?.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '') + '_amount')
            };


            return merged;
        });
        return mergedDetails;

    } catch (error) {
        console.error('Error in mergeAllowanceDetailsWithDatabase:', error);
        throw error;
    }
}
async function buildComponentAllowanceMapping(organizationDbConnection, mergedAllowanceDetails) {
    try {
        console.log('🔧 STEP 1: Building Component-to-Allowance Mapping...');

        const componentMap = new Map();

        const salaryComponents = await organizationDbConnection('salary_components')
            .select('Component_Id', 'Component_Code', 'Component_Name', 'Component_Type');

        // Create Component_Id -> Component_Code mapping
        const componentIdToCode = new Map();
        salaryComponents.forEach(comp => {
            componentIdToCode.set(comp.Component_Id, comp.Component_Code);
        });

        // Build dynamic mapping for each allowance
        for (const allowance of mergedAllowanceDetails) {
            if (allowance.Salary_Component_Id) {
                const componentCode = componentIdToCode.get(allowance.Salary_Component_Id);

                if (componentCode) {
                    componentMap.set(componentCode, {
                        allowanceTypeId: allowance.Allowance_Type_Id,
                        allowanceName: allowance.Allowance_Name,
                        percentage: allowance.Percentage,
                        amount: allowance.Amount,
                        calculationType: allowance.Calculation_Type,
                        formula: allowance.Custom_Formula,
                        salaryComponentId: allowance.Salary_Component_Id,
                        benefitForms: allowance.BenefitForms, // For bonus wage calculation
                        allowanceMode: allowance.Allowance_Mode // For bonus vs non-bonus check
                    });


                }
            }
        }

        console.log(`🔧 STEP 1 Complete: Built mapping for ${componentMap.size} components`);
        return componentMap;

    } catch (error) {
        console.error('❌ STEP 1 Failed: Error building component mapping:', error);
        throw error;
    }
}

/**
 * STEP 2: Formula Dependency Analyzer
 * Analyzes formulas to extract component dependencies
 * Builds dependency graph for priority-based calculation
 */
function analyzeFormulaDependencies(mergedAllowanceDetails, componentMap) {
    try {
        console.log('🔧 STEP 2: Analyzing Formula Dependencies...');

        const dependencyGraph = new Map();
        const componentDependencies = new Map();

        for (const allowance of mergedAllowanceDetails) {
            const componentCode = getComponentCodeForAllowance(allowance, componentMap);

            if (componentCode) {
                let dependencies = [];

                if (allowance.Calculation_Type === 'Custom Formula' && allowance.Custom_Formula) {
                    // Extract dependencies from formula using simple pattern matching
                    dependencies = extractFormulaDependencies(allowance.Custom_Formula);

                } else if (allowance.Calculation_Type === 'Percentage') {
                    // Percentage calculations depend on base components (basic_salary_amount, monthly_ctc_amount, etc.)
                    // IDENTICAL to original calculateSalary.js logic
                    if (allowance.Allowance_Mode === 'Bonus') {
                        dependencies = ['monthly_ctc_amount']; // Bonus percentages typically use monthly CTC
                    } else {
                        dependencies = ['basic_salary_amount']; // Regular percentages typically use basic salary
                    }

                } else if (allowance.Calculation_Type === 'Amount') {
                    // Fixed amount - no dependencies
                    dependencies = [];

                } else {
                    // Unknown calculation type
                    dependencies = [];
                    console.log(`  ⚠️  ${allowance.Allowance_Name}: Unknown calculation type '${allowance.Calculation_Type}' (no dependencies assumed)`);
                }

                dependencyGraph.set(componentCode, {
                    allowance: allowance,
                    dependencies: dependencies,
                    dependencyCount: dependencies.length,
                    resolved: false
                });

                componentDependencies.set(componentCode, dependencies);
            }
        }

        console.log(`🔧 STEP 2 Complete: Analyzed ${dependencyGraph.size} components`);
        return { dependencyGraph, componentDependencies };

    } catch (error) {
        console.error('❌ STEP 2 Failed: Error analyzing dependencies:', error);
        throw error;
    }
}

/**
 * Helper: Get component code for allowance from mapping
 */
function getComponentCodeForAllowance(allowance, componentMap) {
    // Find component code by matching allowance configuration
    for (const [componentCode, config] of componentMap.entries()) {
        if (config.allowanceTypeId === allowance.Allowance_Type_Id) {
            return componentCode;
        }
    }
    return null;
}

/**
 * STEP 3: Priority-Based Calculation Engine
 * Creates priority queues based on dependency count
 * Components with fewer dependencies get calculated first
 * FIXED: Properly handles resolved vs unresolved dependencies
 */
function buildPriorityCalculationQueue(dependencyGraph, componentValues) {
    try {
        console.log('🔧 STEP 3: Building Priority Calculation Queue...');

        const priorityQueues = new Map();
        const maxPriority = 10; // Prevent infinite loops

        // Track which components are actually resolved (not just initialized to 0)
        const resolvedComponents = new Set();

        // Mark input components as resolved (CTC values only - retirals are calculated separately)
        const inputComponents = ['annual_ctc_amount', 'monthly_ctc_amount', 'cost_to_company_amount'];
        inputComponents.forEach(comp => {
            if (componentValues.hasOwnProperty(comp) && componentValues[comp] !== null && componentValues[comp] !== undefined) {
                resolvedComponents.add(comp);
            }
        });

        // FIXED: Don't mark retiral components as resolved - they need to be calculated in Step 2
        // pf_employer_share, admin_charge, edli_charge, etc. should NOT be in inputComponents

        // Mark percentage-based and amount-based components as resolved if they have their values
        for (const [componentCode, graphNode] of dependencyGraph.entries()) {
            if ((graphNode.allowance?.Calculation_Type === 'Percentage' ||
                 graphNode.allowance?.Calculation_Type === 'Amount') &&
                componentValues.hasOwnProperty(componentCode) &&
                componentValues[componentCode] > 0) {
                resolvedComponents.add(componentCode);
            }
        }



        // Group components by dependency count (priority)
        for (const [componentCode, graphNode] of dependencyGraph.entries()) {
            // FIXED: Check against resolvedComponents set, not just non-zero values
            const unresolvedDependencies = graphNode.dependencies.filter(dep =>
                !resolvedComponents.has(dep)
            );

            const priority = unresolvedDependencies.length;

            if (priority > maxPriority) {
                console.warn(`⚠️  ${componentCode} has too many unresolved dependencies (${priority}), may indicate circular dependency`);
                continue;
            }

            if (!priorityQueues.has(priority)) {
                priorityQueues.set(priority, []);
            }

            priorityQueues.get(priority).push({
                componentCode,
                allowance: graphNode.allowance,
                dependencies: graphNode.dependencies,
                unresolvedDependencies
            });
        }

        // Sort priority queues (0 = highest priority, no dependencies)
        const sortedPriorities = Array.from(priorityQueues.keys()).sort((a, b) => a - b);



        console.log(`🔧 STEP 3 Complete: Built ${sortedPriorities.length} priority levels`);
        return { priorityQueues, sortedPriorities, resolvedComponents };

    } catch (error) {
        console.error('❌ STEP 3 Failed: Error building priority queue:', error);
        throw error;
    }
}

/**
 * STEP 4: Multi-Pass Iterative Resolution Engine
 * Executes calculation in multiple passes:
 * Pass 1: Non-retirals with no dependencies
 * Pass 2: Retirals using Pass 1 results
 * Pass 3: Remaining non-retirals using retiral results
 * Continue until all resolved or max iterations reached
 * FIXED: Properly tracks resolved components
 */
async function executeMultiPassCalculation(priorityQueues, sortedPriorities, componentValues, componentMap, organizationDbConnection, roundOffSettings, formId, resolvedComponents = new Set()) {
    try {
        const maxIterations = 5;
        let iteration = 0;
        let totalResolved = 0;
        let resolvedInThisIteration = 0;

        do {
            iteration++;
            resolvedInThisIteration = 0;

            for (const priority of sortedPriorities) {
                const queue = priorityQueues.get(priority);

                for (const item of queue) {
                    if (item.resolved) continue;
                    const unresolvedDeps = item.dependencies.filter(dep =>
                        !resolvedComponents.has(dep)
                    );

                    if (unresolvedDeps.length === 0) {
                        const calculatedValue = await calculateSingleComponent(
                            item.allowance,
                            componentValues,
                            componentMap,
                            organizationDbConnection,
                            roundOffSettings,
                            formId
                        );
                        if (calculatedValue !== null && calculatedValue !== undefined) {
                            componentValues[item.componentCode] = calculatedValue;
                            resolvedComponents.add(item.componentCode); // FIXED: Track as resolved
                            item.resolved = true;
                            resolvedInThisIteration++;
                            totalResolved++;


                        } else {
                            console.log(`  ⚠️  Failed to calculate ${item.allowance.Allowance_Name}`);
                        }
                    } else {
                        console.log(`  ⏳ ${item.allowance.Allowance_Name} waiting for: [${unresolvedDeps.join(', ')}]`);
                    }
                }
            }

            console.log(`🔄 Iteration ${iteration} complete: Resolved ${resolvedInThisIteration} components`);

        } while (resolvedInThisIteration > 0 && iteration < maxIterations);

        console.log(`🔧 STEP 4 Complete: Resolved ${totalResolved} components in ${iteration} iterations`);
        return { totalResolved, iterations: iteration };

    } catch (error) {
        console.error('❌ STEP 4 Failed: Error in multi-pass calculation:', error);
        throw error;
    }
}

/**
 * Helper: Calculate Single Component Value
 * Handles different calculation types: Amount, Percentage, Custom Formula
 * EXACTLY IDENTICAL to original calculateSalary.js logic for non-custom formulas
 */
async function calculateSingleComponent(allowance, componentValues, componentMap, organizationDbConnection, roundOffSettings, formId) {
    try {
        console.log(`🔧 Calculating ${allowance.Allowance_Name} (${allowance.Calculation_Type})`);

        if (allowance.Calculation_Type === 'Custom Formula' && allowance.Custom_Formula) {
            // Formula calculation
            const { evaluateFormula } = require('./formulaEvaluation');
            const result = await evaluateFormula(allowance.Custom_Formula, componentValues, organizationDbConnection);

            if (result.success) {
                const calculatedValue = getRoundOffValue(formId.salary, result.result, roundOffSettings);

                return calculatedValue;
            } else {
                console.error(`  ❌ Formula evaluation failed: ${result.error}`);
                return null;
            }

        } else if (allowance.Calculation_Type === 'Percentage') {
            // DYNAMIC: Check if this component is used in formulas to determine return type
            const componentCode = allowance.Salary_Component_Code || allowance.Component_Code;

            // Check if this component is referenced in any formulas
            const isUsedInFormulas = componentMap && Array.from(componentMap.values()).some(comp =>
                comp.formula && comp.formula.includes(componentCode)
            );

            if (isUsedInFormulas) {
                // Component used in formulas: return percentage value for formula calculations
                const percentageValue = allowance.Percentage || 0;

                return percentageValue;
            } else {
                // Component not used in formulas: calculate the actual amount
                const baseValue = await determinePercentageBase(allowance, componentValues, componentMap);
                const calculatedValue = (baseValue * (allowance.Percentage || 0)) / 100;
                const roundedValue = getRoundOffValue(formId.salary, calculatedValue, roundOffSettings);

                return roundedValue;
            }

        } else if (allowance.Calculation_Type === 'Amount') {
            // IDENTICAL to original calculateSalary.js logic - Fixed amount
            const roundedValue = getRoundOffValue(formId.salary, allowance.Amount || 0, roundOffSettings);

            return roundedValue;

        } else {
            console.warn(`  ⚠️  Unknown calculation type: ${allowance.Calculation_Type}`);
            return null;
        }

    } catch (error) {
        console.error(`❌ Error calculating ${allowance.Allowance_Name}:`, error);
        return null;
    }
}

/**
 * Helper: Determine base value for percentage calculations
 * IDENTICAL to original calculateSalary.js logic - uses different bases for bonus vs non-bonus
 */
async function determinePercentageBase(allowance, componentValues, componentMap) {
    const basicPay = componentValues.basic_salary_amount || componentValues.base_pay_amount || 0;

    // Apply period multiplier (IDENTICAL to original logic)
    const period = {
        'Quarterly': 3,
        'HalfYearly': 6,
        'Monthly': 1,
        'Annually': 12
    };

    const periodMultiplier = period[allowance.Period] || 1;

    // FIXED: Use correct base calculation for bonus vs non-bonus (IDENTICAL to original calculateSalary.js)
    if (allowance.Allowance_Mode?.toLowerCase() === 'bonus') {
        const bonusFormId = '46'; // From appconstants.js: formId.bonus = 46
        let bonusWages = basicPay;



        // Add non-bonus allowances that have bonusFormId (46) in their BenefitForms
        // This replicates getStatutorySalary logic exactly
        if (componentMap && componentMap.size > 0) {
            for (const [componentCode, allowanceConfig] of componentMap) {
                // Skip bonus allowances (only include non-bonus)
                if (allowanceConfig.allowanceMode?.toLowerCase() !== 'bonus') {
                    // Skip basic_salary_amount since we already started with basicPay
                    if (componentCode === 'basic_salary_amount') {
                        console.log(`    Skipped ${componentCode}: Already included in basicPay`);
                        continue;
                    }

                    // Check if BenefitForms contains the bonusFormId (46)
                    const benefitForms = allowanceConfig.benefitForms || '';
                    if (benefitForms && benefitForms.split(',').includes(bonusFormId)) {
                        const componentValue = componentValues[componentCode] || 0;
                        bonusWages += componentValue;
                        console.log(`    Added ${componentCode}: ₹${componentValue} (BenefitForms: ${benefitForms})`);
                    }
                }
            }
        }
        const baseValue = bonusWages * periodMultiplier;
        return baseValue;
    } else {
        const baseValue = basicPay * periodMultiplier;

        return baseValue;
    }
}

/**
 * STEP 5: Analyze Unresolved Components for 3-Step Process
 * FIXED: Don't throw error - identify what needs Step 2 & 3
 * FIXED: Use database-driven retiral component detection
 */
async function analyzeUnresolvedComponents(priorityQueues, sortedPriorities, resolvedComponents, organizationDbConnection) {
    try {
        console.log('🔧 STEP 5: Analyzing Unresolved Components...');

        // FIXED: Get retiral components from database instead of hardcoding
        const retiralComponents = await organizationDbConnection('salary_components')
            .select('Component_Code', 'Component_Name')
            .where('Component_Type', 'RETIRAL');

        const retiralComponentCodes = new Set(retiralComponents.map(comp => comp.Component_Code));


        const unresolvedComponents = [];
        const step2Components = [];
        const step3Components = [];

        for (const priority of sortedPriorities) {
            const queue = priorityQueues.get(priority);

            for (const item of queue) {
                if (!item.resolved) {
                    const unresolvedDependencies = item.dependencies.filter(dep =>
                        !resolvedComponents.has(dep) // FIXED: Use resolvedComponents Set
                    );

                    const componentInfo = {
                        name: item.allowance.Allowance_Name,
                        componentCode: item.componentCode,
                        dependencies: item.dependencies,
                        unresolvedDependencies: unresolvedDependencies
                    };

                    unresolvedComponents.push(componentInfo);

                    // FIXED: Use database-driven retiral component detection
                    const hasRetiralDependency = unresolvedDependencies.some(dep =>
                        retiralComponentCodes.has(dep)
                    );

                    if (hasRetiralDependency) {
                        step2Components.push(componentInfo);
                        console.log(`  📋 Step 2 Component: ${componentInfo.name} (depends on retiral: ${unresolvedDependencies.filter(dep => retiralComponentCodes.has(dep)).join(', ')})`);
                    } else {
                        step3Components.push(componentInfo);
                        console.log(`  📋 Step 3 Component: ${componentInfo.name} (depends on non-retiral: ${unresolvedDependencies.join(', ')})`);
                    }
                }
            }
        }
        return {
            unresolvedComponents,
            step2Components,
            step3Components,
            needsStep2: step2Components.length > 0,
            needsStep3: step3Components.length > 0
        };
    } catch (error) {
        console.error('❌ STEP 5 Failed: Error analyzing components:', error);
        throw error;
    }
}

async function calculateSalaryWithFormulas(args, context, retrievedData) {
    let organizationDbConnection;
    try {
        const { formId } = require('./appconstants');

        const {
            roundOffSettings,
            payrollGeneralSettings,
            providentFundSettings,
            socialSecurityScheme,
            retiralDetails: retrievedRetiralDetails,
            providentFundDetails
        } = retrievedData;
        const inputAllowanceData = args.allowanceDetails ? JSON.parse(args.allowanceDetails) : [];
        const salaryDetails = args.salaryDetails ? JSON.parse(args.salaryDetails) : {};

        if (!salaryDetails.Annual_Ctc || salaryDetails.Annual_Ctc <= 0) {
            throw 'PST0110';
        }

        organizationDbConnection = context.orgdb ? context.orgdb : require('knex')(context.connection.OrganizationDb);

        const mergedAllowanceDetails = await mergeAllowanceDetailsWithDatabase(
            inputAllowanceData,
            organizationDbConnection
        );
        let grossConfigurationFormulas = [];
        if(args.grossIds && args.grossIds.length > 0){
        try {
            const grossConfigs = await organizationDbConnection('gross_configuration as GC')
                .select([
                    'GC.Gross_Id',
                    'GC.Salary_Component_Id',
                    'GC.Gross_Name',
                    'GC.Calculation_Type',
                    'GC.Custom_Formula',
                    'SC.Component_Code',
                    'SC.Component_Name'
                ])
                .leftJoin('salary_components as SC', 'SC.Component_Id', 'GC.Salary_Component_Id')
                .where('GC.Status', 'Active')
                .where ('GC.Calculation_Type', 'Custom Formula')
                .whereIn('GC.Gross_Id', args.grossIds)
                .orderBy('GC.Gross_Id');
            grossConfigurationFormulas = grossConfigs.map(config => ({
                Allowance_Type_Id: `gross_${config.Gross_Id}`,
                Allowance_Name: config.Gross_Name,
                Amount: 0,
                Calculation_Type: config.Calculation_Type,
                Custom_Formula: config.Custom_Formula,
                Component_Code: config.Component_Code,
                Salary_Component_Id: config.Salary_Component_Id,
                Salary_Component_Code: config.Component_Code
            }));
        } catch (error) {
            grossConfigurationFormulas = [];
        }
    }else{
        grossConfigurationFormulas = [];
    }

        const grossComponents = grossConfigurationFormulas.reverse();
        const allComponents = [...mergedAllowanceDetails, ...grossComponents];

        const annualCTC = getRoundOffValue(formId.salary, salaryDetails.Annual_Ctc, roundOffSettings);
        const monthlyCTC = getRoundOffValue(formId.salary, annualCTC / 12, roundOffSettings);

        const componentMap = await buildComponentAllowanceMapping(organizationDbConnection, allComponents);

        const { dependencyGraph } = analyzeFormulaDependencies(allComponents, componentMap);


        const componentValues = await buildInitialComponentValues(
            monthlyCTC,
            organizationDbConnection,
            mergedAllowanceDetails,
            annualCTC
        );
        componentValues.monthly_ctc_amount = monthlyCTC;
        componentValues.annual_ctc_amount = annualCTC;

        const { priorityQueues, sortedPriorities, resolvedComponents } = buildPriorityCalculationQueue(dependencyGraph, componentValues);
        await executeMultiPassCalculation(
            priorityQueues,
            sortedPriorities,
            componentValues,
            componentMap,
            organizationDbConnection,
            roundOffSettings,
            formId,
            resolvedComponents
        );

        const analysisResult = await analyzeUnresolvedComponents(priorityQueues, sortedPriorities, resolvedComponents, organizationDbConnection);
        const finalAllowances = [];

        for (const allowance of mergedAllowanceDetails) {
            // if (allowance.Allowance_Mode?.toLowerCase() === 'bonus') {
            //     continue;
            // }

            const componentCode = allowance.Salary_Component_Code || allowance.Component_Code;
            let calculatedValue = componentValues[componentCode];

            if (!calculatedValue && allowance.Calculation_Type === 'Amount') {
                calculatedValue = parseFloat(allowance.Amount) || parseFloat(allowance.Amount) || 0;
            }

            if (calculatedValue !== undefined && calculatedValue !== 0) {
                const allowanceResult = {
                    Allowance_Type_Id: allowance.Allowance_Type_Id,
                    Allowance_Name: allowance.Allowance_Name,
                    Amount: calculatedValue,
                    Calculation_Type: allowance.Calculation_Type,
                    Custom_Formula: allowance.Custom_Formula,
                    Percentage: allowance.Percentage,
                    Allowance_Mode: allowance.Allowance_Mode,
                    Period: allowance.Period,
                    Is_Basic_Pay: allowance.Is_Basic_Pay,
                    Formula_Based: allowance.Formula_Based,
                    Component_Code: componentCode
                };

                finalAllowances.push(allowanceResult);
            }
        }

        for (const grossComponent of grossComponents) {
            const componentCode = grossComponent.Component_Code;
            const calculatedValue = componentValues[componentCode];

            if (calculatedValue !== undefined && calculatedValue !== 0) {
                const grossAllowanceResult = {
                    Allowance_Type_Id: grossComponent.Allowance_Type_Id,
                    Allowance_Name: grossComponent.Allowance_Name,
                    Amount: calculatedValue,
                    Calculation_Type: grossComponent.Calculation_Type,
                    Custom_Formula: grossComponent.Custom_Formula,
                    Formula_Based: 'yes', // Keep for backward compatibility
                    Is_Basic_Pay: 'No', // Keep for backward compatibility
                    Component_Code: componentCode,
                    IsGrossComponent: true
                };

                finalAllowances.push(grossAllowanceResult);
            }
        }

        let basicPay = 0;

        const basicPayAllowance = finalAllowances.find(item =>
            item.Component_Code?.toLowerCase() === 'basic_salary_amount'
        );

        if (basicPayAllowance) {
            basicPay = basicPayAllowance.Amount;
        }
        await validateRetiralCircularDependencies(mergedAllowanceDetails, formId);

        const retiralDetails = retrievedRetiralDetails || (args.retiralDetails?.length > 0 ? JSON.parse(args.retiralDetails) : []);

        // Check if all components are resolved
        let { allComponentsResolved, unresolvedComponents } = checkUnresolvedComponents(priorityQueues);
        if (retiralDetails.length > 0 && !allComponentsResolved) {
        
        const providentFundConfigurationValue = args.providentFundConfigurationValue;

         const pf = await calculateProvidentFundDetails(
                providentFundDetails,
                basicPay,
                finalAllowances,
                providentFundSettings,
                socialSecurityScheme,
                payrollGeneralSettings,
                providentFundConfigurationValue,
                roundOffSettings
            );
        componentValues.pf_employer_share=getRoundOffValue(formId.pfId, pf.Employer_Share_Amount, roundOffSettings);
        resolvedComponents.add('pf_employer_share');

        // Calculate NPS if configured
        const npsRetirals = retiralDetails.find((item) => item.Form_Id === formId.npsId);
        if (npsRetirals) {
            // Get NPS slab details if slab-wise NPS is enabled
            let npsSlabDetails = null;
            if (payrollGeneralSettings?.Slab_Wise_NPS?.toLowerCase() === 'yes') {
                npsSlabDetails = await organizationDbConnection(ehrTables.npsSlab)
                    .select('*');
            }

            // Use local calculateNPSDetails function (defined in this file)
            const nps = await calculateNPSDetails(
                finalAllowances,
                basicPay,
                npsRetirals,
                npsSlabDetails,
                payrollGeneralSettings,
                roundOffSettings
            );
         if (nps && nps.Employer_Share_Amount != null) {
                componentValues.nps_employer_share = getRoundOffValue(formId.npsId, nps.Employer_Share_Amount, roundOffSettings);
                resolvedComponents.add('nps_employer_share');
            }
        }

        await executeMultiPassCalculation(
            priorityQueues,
            sortedPriorities,
            componentValues,
            componentMap,
            organizationDbConnection,
            roundOffSettings,
            formId,
            resolvedComponents
        );
    }
    // Re-check if all components are resolved after multi-pass calculation
    ({ allComponentsResolved, unresolvedComponents } = checkUnresolvedComponents(priorityQueues));
    if(!allComponentsResolved){
        const errorDetails = unresolvedComponents.map(item =>
            `Allowance '${item.Allowance_Name}' depends on: ${item.dependencies.join(', ')}`
        ).join('; ');
        const validationError = {};
        validationError['IVE0724'] = `Cannot calculate salary - Circular dependency detected. ${errorDetails}. This creates an infinite loop. Please review the formulas and remove circular references.`;

        // Store validationError in error object so catch block can access it
        const error = new Error('IVE0000');
        error.validationError = validationError;
        throw error;
    }

        // Add gross components to final allowances
        for (const grossComponent of grossComponents) {
            const componentCode = grossComponent.Component_Code;
            const calculatedValue = componentValues[componentCode];

            if (calculatedValue !== undefined && calculatedValue !== 0) {
                const grossAllowanceResult = {
                    Allowance_Type_Id: grossComponent.Allowance_Type_Id,
                    Allowance_Name: grossComponent.Allowance_Name,
                    Amount: calculatedValue,
                    Calculation_Type: grossComponent.Calculation_Type,
                    Custom_Formula: grossComponent.Custom_Formula,
                    Formula_Based: 'yes', // Keep for backward compatibility
                    Is_Basic_Pay: 'No', // Keep for backward compatibility
                    Component_Code: componentCode,
                    IsGrossComponent: true
                };

                finalAllowances.push(grossAllowanceResult);
            }
        }

        // Add remaining calculated components to final allowances
        for (const allowance of mergedAllowanceDetails) {
            // FIXED: Skip bonus allowances previously But now we are using formula for Bonus as well 
            // if (allowance.Allowance_Mode?.toLowerCase() === 'bonus') {
            //     continue; // Skip bonus allowances
            // }

            const componentCode = allowance.Salary_Component_Code || allowance.Component_Code;
            const calculatedValue = componentValues[componentCode];
            // Check if this allowance is already in finalAllowances
            const existingAllowance = finalAllowances.find(fa => fa.Allowance_Type_Id === allowance.Allowance_Type_Id);

            if (!existingAllowance && calculatedValue !== undefined && calculatedValue !== 0) {
                const allowanceResult = {
                    Allowance_Type_Id: allowance.Allowance_Type_Id,
                    Allowance_Name: allowance.Allowance_Name,
                    Amount: calculatedValue,
                    Calculation_Type: allowance.Calculation_Type,
                    Custom_Formula: allowance.Custom_Formula,
                    Percentage: allowance.Percentage,
                    Allowance_Mode: allowance.Allowance_Mode,
                    Period: allowance.Period,
                    Is_Basic_Pay: allowance.Is_Basic_Pay,
                    Formula_Based: allowance.Formula_Based,
                    Component_Code: componentCode
                };

                finalAllowances.push(allowanceResult);
            }
        }
        finalAllowances.forEach(item => {
                if(item.Calculation_Type.toLowerCase() === 'custom formula'){
                    item.Amount = getRoundOffValue(formId.salary, item.Amount/12, roundOffSettings);
                }
            });
        return finalAllowances;

    } catch (error) {
       throw error;
    } finally {
        if (!context.orgdb && organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}

/**
 * Check if all components are resolved and return unresolved components
 * @param {Map} priorityQueues - Priority queues containing calculation items
 * @returns {Object} - Object containing allComponentsResolved flag and unresolvedComponents array
 */
function checkUnresolvedComponents(priorityQueues) {
    try {
        let allComponentsResolved = true;
        let unresolvedComponents = [];

        for (const [, queue] of priorityQueues) {
            for (const item of queue) {
                if (!item.resolved) {
                    if (allComponentsResolved) {
                        allComponentsResolved = false;
                    }
                    unresolvedComponents.push({
                        Allowance_Type_Id: item.allowance.Allowance_Type_Id,
                        Allowance_Name: item.allowance.Allowance_Name,
                        dependencies: item.dependencies
                    });
                }
            }
        }

        return {
            allComponentsResolved,
            unresolvedComponents
        };
    } catch (error) {
        console.error('Error in checkUnresolvedComponents:', error);
        throw error;
    }
}

async function buildInitialComponentValues(monthlyCTC, organizationDbConnection, allowanceData = [], annualCTC = 0) {
    try {
        const componentValues = {};

        const allFormulas = [];

        const grossConfigs = await organizationDbConnection('gross_configuration as GC')
            .select(['GC.Custom_Formula', 'SC.Component_Code'])
            .leftJoin('salary_components as SC', 'SC.Component_Id', 'GC.Salary_Component_Id')
            .where('GC.Status', 'Active');

        grossConfigs.forEach(config => {
            if (config.Custom_Formula) {
                allFormulas.push(config.Custom_Formula);
            }
        });

        allowanceData.forEach(allowance => {
            if (allowance.Custom_Formula) {
                allFormulas.push(allowance.Custom_Formula);
            }
        });

        const referencedComponents = new Set();
        const formulaPattern = /\b([a-z_]+_amount|[a-z_]+_percentage|[a-z_]+_share|gross_inclusive_of_retirals|monthly_ctc_amount|annual_ctc_amount|cost_to_company_amount|pf_employer_share|admin_charge|edli_charge|insurance_employer_share|nps_employer_share|gratuity_amount)\b/g;

        allFormulas.forEach(formula => {
            const matches = formula.match(formulaPattern);
            if (matches) {
                matches.forEach(match => referencedComponents.add(match));
            }
        });



        // Initialize all referenced components to 0
        referencedComponents.forEach(componentCode => {
            componentValues[componentCode] = 0;
        });

        // FIXED: Also include all allowance component codes (even if not referenced in formulas)
        allowanceData.forEach(allowance => {
            const componentCode = allowance.Salary_Component_Code || allowance.Component_Code;
            if (componentCode && !componentValues.hasOwnProperty(componentCode)) {
                componentValues[componentCode] = 0;

            }
        });

        const inputMappings = {
            'cost_to_company_amount': annualCTC,
            'annual_ctc_amount': annualCTC,
            'monthly_ctc_amount': monthlyCTC
        };

        Object.keys(inputMappings).forEach(componentCode => {
            if (componentValues.hasOwnProperty(componentCode)) {
                componentValues[componentCode] = inputMappings[componentCode];

            }
        });

        // FIXED: Set percentage and amount values from payload, not just 0
        allowanceData.forEach(allowance => {
            if (allowance.Component_Code && componentValues.hasOwnProperty(allowance.Component_Code)) {
                const calcType = allowance.Calculation_Type?.toLowerCase();

                if (calcType === 'fixed' || calcType === 'amount') {
                    const amount = parseFloat(allowance.Amount) || parseFloat(allowance.Amount) || 0;
                    if (amount > 0) {
                        componentValues[allowance.Component_Code] = amount;
                    }
                }
                else if (calcType === 'percentage') {
                    const percentage = parseFloat(allowance.Percentage) || parseFloat(allowance.Percentage) || 0;
                    if (percentage > 0) {
                        componentValues[allowance.Component_Code] = percentage;
                    }
                }
            }
        });

        return componentValues;

    } catch (error) {
        return {
            cost_to_company_amount: annualCTC,
            monthly_ctc_amount: monthlyCTC
        };
    }
}

module.exports = {
  getWorkflowProcessInstanceId,
  getOneTimeEarningWorkflowProcessInstanceId,
  initaiteWorkflow,
  getEventId,
  getRoundOffSettings,
  getRoundOffValue,
  deleteOldApprovalRecordsWithoutTrx,
  checkSalaryMigrationAllowed,
  validateCommonRuleInput,
  getFiscalStartDate,
  validateCandidateStatus,
  revertCandidateStatusOnDelete,
  // Formula-based salary calculation functions
  calculateSalaryWithFormulas,
  mergeAllowanceDetailsWithDatabase,
  buildInitialComponentValues,
  checkUnresolvedComponents,
  // NEW: Dynamic Component Resolution System
  buildComponentAllowanceMapping,
  analyzeFormulaDependencies,
  buildPriorityCalculationQueue,
  executeMultiPassCalculation,
  calculateSingleComponent,
  determinePercentageBase,
  analyzeUnresolvedComponents,
  getComponentCodeForAllowance,
  // PF calculation functions
  calculateProvidentFundDetails,
  calculateCurrentProvidentFundDetails,
  calculateSlabWisePf,
  getPfSalary,
  getProvidentFundWageBasedOnContributionRate,
  // NPS calculation functions
  calculateNPSDetails,
  calculateSlabWiseNps,
  getStatutorySalary,
  // Internal helper functions (used within this module)
  extractFormulaDependencies,
  validateRetiralCircularDependencies,
  updateWorkflowProcessInstanceId,
  getFiscalMonthYear,
  // Bulk component calculation
  getEmployeeComponentValuesInBulk,
  checkForRuntimeComponents
};
function extractFormulaDependencies(formula) {
    const dependencies = new Set();

    // Match all component patterns: word_amount, word_percentage, word_share, gross_inclusive_of_retirals, etc.
    const componentPattern = /\b([a-z_]+(?:_amount|_percentage|_share|_retirals|_ctc))\b/gi;
    let match;

    while ((match = componentPattern.exec(formula)) !== null) {
        dependencies.add(match[1].toLowerCase());
    }

    return Array.from(dependencies);
}

/**
 * Validates that retiral calculations don't have circular dependencies
 * @param {Array} mergedAllowanceDetails - All allowance configurations
 * @param {Object} formId - Form IDs for different retirals
 * @throws {Error} If circular dependency is detected
 */
async function validateRetiralCircularDependencies(mergedAllowanceDetails, formId) {
    const retiralValidations = [
        { name: 'PF', formId: formId.pfId, componentCode: 'pf_employer_share' },
        { name: 'NPS', formId: formId.npsId, componentCode: 'nps_employer_share' },
        { name: 'Insurance', formId: formId.insurance, componentCode: 'insurance_employer_share' },
        { name: 'Gratuity', formId: formId.gratuityId, componentCode: 'gratuity_amount' },
        { name: 'Bonus', formId: formId.bonus, componentCode: 'bonus_amount' }
    ];

    for (const retiral of retiralValidations) {
        if (!retiral.formId) continue; // Skip if form ID not provided

        // Find allowances that depend on this retiral (have this retiral's form ID in BenefitForms)
        const dependentAllowances = mergedAllowanceDetails.filter(allowance =>
            allowance.BenefitForms && allowance.BenefitForms.split(',').includes(retiral.formId.toString() && !allowance.Amount)
        );

        // Check if any dependent allowance has a formula that uses this retiral component
        for (const allowance of dependentAllowances) {
            if (allowance.Custom_Formula && allowance.Custom_Formula.includes(retiral.componentCode)) {
                throw new Error(
                    `Cannot calculate ${retiral.name}: Circular dependency detected. ` +
                    `Allowance '${allowance.Allowance_Name}' depends on ${retiral.name} (Form_Id: ${retiral.formId}) ` +
                    `but its formula uses '${retiral.componentCode}'. This creates an infinite loop.`
                );
            }
        }
    }
}
function getPfSalary(basicPay, allowanceDetails, providentFundSettings, providentFundDetails, actualBasicPay, pfAdhocAllowanceAmount = 0, Provident_Fund_Configuration_Value) {
    try {
        let basicPlusAllowance = basicPay + (parseFloat(pfAdhocAllowanceAmount) || 0);
        const benefitFormId = formId.pfId.toString();
        const restrictedWage = providentFundSettings?.Restricted_PF_Wage_Amount || 15000;
        // Check if we should return basic + allowance immediately
        if (Provident_Fund_Configuration_Value?.toLowerCase() === 'current' &&
            actualBasicPay >= restrictedWage &&
            providentFundDetails?.PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit?.toLowerCase() === 'yes') {
            return basicPlusAllowance;
        }

        // Process allowances based on configuration
        if (allowanceDetails?.length > 0) {
            // First pass: Process allowances based on current configuration
            for (const allowance of allowanceDetails) {
                if (allowance?.BenefitForms?.split(',').includes(benefitFormId)) {
                    // Include allowance if:
                    // 1. Consider_For_EPF_Contribution is 'always', OR
                    // 2. It's a formula-based allowance (Fixed Allowance should always be included in PF wages)
                    const shouldInclude = allowance.Consider_For_EPF_Contribution?.toLowerCase() === 'always' ||
                                        allowance.Component_Code?.toLowerCase() === 'fixed_allowance_amount';

                    if (shouldInclude) {
                        basicPlusAllowance += parseFloat(allowance.Amount || 0);
                    }
                }
            }

            // Second pass: Process LOP conditions if applicable
            if (providentFundDetails?.Provident_Fund_Configuration?.toLowerCase() === 'current' &&
                providentFundDetails?.Consider_All_Salary_Components_For_LOP?.toLowerCase() === 'yes') {

                for (const allowance of allowanceDetails) {
                    if (allowance?.BenefitForms?.split(',').includes(benefitFormId) &&
                        allowance.Consider_For_EPF_Contribution?.toLowerCase() === 'only when pf wage is less than ₹15,000' &&
                        basicPlusAllowance < restrictedWage) {

                        basicPlusAllowance += parseFloat(allowance.Amount || 0);
                        if (basicPlusAllowance > restrictedWage) {
                            basicPlusAllowance = restrictedWage;
                        }
                    }
                }
            }
        }

        return basicPlusAllowance;
    } catch (error) {
        console.error('Error in getPfSalary', error);
        throw error
    }
}

/**
 * Calculates the Provident Fund wage based on contribution rate
 * @param {string} employeeContributionRate - Employee's contribution rate setting
 * @param {string} employerContributionRate - Employer's contribution rate setting
 * @param {number} employeePfWage - Employee's PF wage
 * @param {Object} providentFundSettings - PF settings from database
 * @returns {Object} Object containing Employee_Pf_Wage and Employer_Pf_Wage
 */
function getProvidentFundWageBasedOnContributionRate(employeeContributionRate, employerContributionRate, employeePfWage, providentFundSettings) {
    try {
        let employeeWage = employeePfWage;
        let employerWage = employeePfWage;

        // Calculate employee wage based on contribution rate
        if (employeeContributionRate?.toLowerCase() === 'restrict') {
            employeeWage = Math.min(employeePfWage, providentFundSettings.Restricted_PF_Wage_Amount || 15000);
        }

        // Calculate employer wage based on contribution rate
        if (employerContributionRate?.toLowerCase() === 'restrict') {
            employerWage = Math.min(employeePfWage, providentFundSettings.Restricted_PF_Wage_Amount || 15000);
        }

        return {
            Employee_Pf_Wage: employeeWage,
            Employer_Pf_Wage: employerWage
        };
    } catch (error) {
        console.error('Error in getProvidentFundWageBasedOnContributionRate', error);
        throw error
    }
}

/**
 * Calculates current Provident Fund details including employee and employer contributions
 * @param {number} employeePfWage - Employee's PF wage
 * @param {Object} providentFundSettings - PF settings from database
 * @param {Object} providentFundDetails - Employee's PF details
 * @returns {Object} Object containing PF details including shares and charges
 */
async function calculateCurrentProvidentFundDetails(employeePfWage, providentFundSettings, providentFundDetails) {
    try {

        if (providentFundDetails?.Retirals_Type?.toLowerCase() === 'fixed') {
            return {
                Employer_Share_Amount: providentFundDetails.Employer_Share_Amount,
                Employee_Share_Amount: providentFundDetails.Employee_Share_Amount,
                Admin_Edli_Charges: {
                    Admin_Edli_Charges: 0,
                    Admin_Edli_Charges_Amount: 0
                },
                Employee_Provident_Fund_Wage: null,
                Retirals_Id: providentFundDetails.Retirals_Id,
                Employee_Retiral_Wages: null,
                Employer_Retiral_Wages: null,
                Form_Id: providentFundDetails.Form_Id,
            };
        } else {
            // Get wages based on contribution rates
            const pfWageBasedOnContributionRate = getProvidentFundWageBasedOnContributionRate(
                providentFundDetails?.PF_Employee_Contribution,
                providentFundDetails?.PF_Employer_Contribution,
                employeePfWage,
                providentFundSettings
            );

            const { Employee_Pf_Wage: employeeWage, Employer_Pf_Wage: employerWage } = pfWageBasedOnContributionRate;

            // Calculate base PF contributions
            let employeeShareAmount = (employeeWage * (providentFundDetails.Employee_Share_Percentage || 12)) / 100;
            let employerShareAmount = (employerWage * (providentFundDetails.Employer_Share_Percentage || 12)) / 100;

            // Round off the amounts
            employeeShareAmount = getRoundOffValue(formId.pfId, employeeShareAmount);
            employerShareAmount = getRoundOffValue(formId.pfId, employerShareAmount);

            // Calculate admin charge if applicable
            let adminChargeAmount = 0;
            if (providentFundDetails.Admin_Charge_Part_Of_CTC?.toLowerCase() === 'yes') {
                const adminCharge = parseFloat(providentFundSettings.Admin_Charge) || 0;
                const adminChargeMaxAmount = parseFloat(providentFundSettings.Admin_Charge_Max_Amount) || Infinity;
                adminChargeAmount = getRoundOffValue(formId.pfId, Math.min(employeeWage * (adminCharge / 100), adminChargeMaxAmount));
            }

            // Calculate EDLI charge if applicable
            let edliChargeAmount = 0;
            if (providentFundDetails.Edli_Charge_Part_Of_CTC?.toLowerCase() === 'yes') {
                const edliConfigurationEmployer = parseFloat(providentFundSettings.EDLI_Charge) || 0;
                const edliChargeMaxAmount = parseFloat(providentFundSettings.EDLI_Charge_Max_Amount) || Infinity;
                edliChargeAmount = getRoundOffValue(formId.pfId, Math.min(employeeWage * (edliConfigurationEmployer / 100), edliChargeMaxAmount));
            }

            // Prepare and return the result
            const adminEdliCharges = {
                Employee_Admin_Charge: adminChargeAmount,
                Employee_Edli_Charge: edliChargeAmount
            };
            return {
                Employer_Share_Amount: employerShareAmount,
                Employee_Share_Amount: employeeShareAmount,
                Admin_Edli_Charges: adminEdliCharges,
                Employee_Provident_Fund_Wage: employeeWage,
                Retirals_Id: providentFundDetails.Retirals_Id,
                Employee_Retiral_Wages: employeeWage,
                Employer_Retiral_Wages: employerWage,
                Form_Id: providentFundDetails.Form_Id,
            };
        }


    } catch (error) {
        console.error('Error in calculateCurrentProvidentFundDetails:', error);
        throw error;
    }
}

/**
 * Calculates slab-wise Provident Fund (PF) contributions based on employee's PF wage and social security scheme slabs
 * @param {Array} socialSecuritySchemeSlabs - Array of social security scheme slabs
 * @param {number} employeePfWage - Employee's PF wage
 * @returns {Object} - Object containing PF contribution details
 */
async function calculateSlabWisePf(socialSecuritySchemeSlabs, employeePfWage) {
    try {
        // Round off the employee's PF wage
        employeePfWage = getRoundOffValue(formId.pfId, employeePfWage);

        // Initialize default return values
        const providentFundDetails = {};
        const adminEdliCharges = {
            Employee_Admin_Charge: 0,
            Employee_Edli_Charge: 0
        };

        // Find the matching slab
        for (const slab of socialSecuritySchemeSlabs) {
            const rangeFrom = parseFloat(slab.Range_From) || 0;
            let rangeTo = parseFloat(slab.Range_To) || 9999999999999.99;

            if (employeePfWage >= rangeFrom && employeePfWage <= rangeTo) {
                // Parse slab values with defaults
                const medianValue = parseFloat(slab.Median_Value) || 0;
                const wisp = parseFloat(slab.WISP) || 0;

                // Parse percentages
                const regularSSEEPercentage = parseFloat(slab.Regular_SS_EE_Percentage) || 0;
                const wispEEPercentage = parseFloat(slab.WISP_EE_Percentage) || 0;
                const regularSSERPercentage = parseFloat(slab.Regular_SS_ER_Percentage) || 0;
                const wispERPercentage = parseFloat(slab.WISP_ER_Percentage) || 0;
                const ecEE = parseFloat(slab.EC_EE) || 0;
                const ecER = parseFloat(slab.EC_ER) || 0;

                // Calculate employee contributions
                const regularSSEE = (medianValue * regularSSEEPercentage) / 100;
                const wispEmployeeShare = (wisp * wispEEPercentage) / 100;
                const sumofEmployeeShare = regularSSEE + wispEmployeeShare + ecEE;

                // Calculate employer contributions
                const regularSSER = (medianValue * regularSSERPercentage) / 100;
                const wispEmployerShare = (wisp * wispERPercentage) / 100;
                const sumofEmployerShare = regularSSER + wispEmployerShare + ecER;

                // Build the result object with rounded values
                return {
                    Retirals_Id: providentFundDetails.Retirals_Id,
                    Form_Id: providentFundDetails.Form_Id,
                    Regular_SS_EE: getRoundOffValue(formId.pfId, regularSSEE),
                    WISP_EE: getRoundOffValue(formId.pfId, wispEmployeeShare),
                    EC_EE: getRoundOffValue(formId.pfId, ecEE),
                    Employee_Share_Amount: getRoundOffValue(formId.pfId, sumofEmployeeShare),
                    Regular_SS_ER: getRoundOffValue(formId.pfId, regularSSER),
                    WISP_ER: getRoundOffValue(formId.pfId, wispEmployerShare),
                    EC_ER: getRoundOffValue(formId.pfId, ecER),
                    Employer_Share_Amount: getRoundOffValue(formId.pfId, sumofEmployerShare),
                    Employee_Provident_Fund_Wage: employeePfWage,
                    Admin_Edli_Charges: {
                        Employee_Admin_Charge: 0,
                        Employee_Edli_Charge: 0
                    }
                };
            }
        }

        // Return empty values if no matching slab is found
        return {
            Regular_SS_EE: 0,
            WISP_EE: 0,
            EC_EE: 0,
            Employee_Share_Amount: 0,
            Regular_SS_ER: 0,
            WISP_ER: 0,
            EC_ER: 0,
            Employer_Share_Amount: 0,
            Employee_Provident_Fund_Wage: 0,
            Admin_Edli_Charges: adminEdliCharges
        };
    } catch (error) {
        console.error('Error in calculateSlabWisePf', error);
        throw error
    }
}

/**
 * Processes provident fund details and calculates contributions
 * @param {Object} providentFundDetails - Employee's provident fund details
 * @param {number} basicPay - Employee's basic pay
 * @param {Array} allowanceDetails - Employee's allowance details
 * @param {Object} providentFundSettings - Provident fund settings
 * @param {Object} socialSecurityScheme - Social security scheme details
 * @param {Object} payrollGeneralSettings - General payroll settings
 * @param {string} providentFundConfigurationValue - PF configuration value
 * @returns {Promise<Object>} - Processed provident fund details
 */
async function calculateProvidentFundDetails(
    providentFundDetails,
    basicPay,
    allowanceDetails,
    providentFundSettings,
    socialSecurityScheme,
    payrollGeneralSettings,
    providentFundConfigurationValue,
    roundOffSettings
) {
    try {
        const employeePfWage = getPfSalary(
            basicPay,
            allowanceDetails,
            providentFundSettings,
            providentFundDetails,
            basicPay,
            0, // pfAdhocAllowanceAmount
            providentFundConfigurationValue
        );

        let providentFundResponse;
        if (payrollGeneralSettings?.Slab_Wise_PF?.toLowerCase() === 'yes') {
            providentFundResponse = await calculateSlabWisePf(socialSecurityScheme, employeePfWage);
        } else {
            providentFundResponse = await calculateCurrentProvidentFundDetails(
                employeePfWage,
                providentFundSettings,
                providentFundDetails
            );
        }

        return {
            'Retirals_Id': providentFundResponse.Retirals_Id || providentFundDetails.Retirals_Id,
            'Form_Id': providentFundResponse.Form_Id || providentFundDetails.Form_Id,
            'Employee_Retiral_Wages': providentFundResponse.Employee_Retiral_Wages,
            'Employer_Retiral_Wages': providentFundResponse.Employer_Retiral_Wages,
            'Employee_Share_Amount': getRoundOffValue(formId.pfId, providentFundResponse.Employee_Share_Amount,
                roundOffSettings),
            'Employer_Share_Amount': getRoundOffValue(formId.pfId, providentFundResponse.Employer_Share_Amount,
                roundOffSettings),
            'Employer_Share_Percentage': providentFundDetails.Employer_Share_Percentage,
            'Employee_Share_Percentage': providentFundDetails.Employee_Share_Percentage,
            'Admin_Charge': getRoundOffValue(formId.pfId, providentFundResponse.Admin_Edli_Charges?.Employee_Admin_Charge || 0,
                roundOffSettings),
            'EDLI_Charge': getRoundOffValue(formId.pfId,providentFundResponse.Admin_Edli_Charges?.Employee_Edli_Charge || 0),
            'Retiral_Type': providentFundDetails?.Retirals_Type
        };
    } catch (error) {
        console.error('Error processing provident fund details:', error);
        throw error;
    }
}

async function calculateNPSDetails(employeeAllowanceDetails, basicPay, npsRetirals, npsDetails, payrollGeneralSettings,roundOffSettings) {
    try {
        let npsResults = {};

        const npsFormId = formId.npsId.toString();

        // Get NPS wages based on allowance details
        const npsWages = getStatutorySalary(employeeAllowanceDetails, basicPay, npsFormId);

        // Get payroll settings to check if NPS is slab-wise
        const isSlabWiseNps = payrollGeneralSettings && payrollGeneralSettings.Slab_Wise_NPS?.toLowerCase() === 'yes' ? true : false;
        if (isSlabWiseNps) {
            // Calculate slab-wise NPS contributions
            const npsSlabDetails = await calculateSlabWiseNps(npsDetails, npsWages);
            npsResults = npsSlabDetails;
        } else {
            if (npsRetirals?.Retirals_Type?.toLowerCase() === 'fixed') {
                npsResults = { Employee_Share_Amount: npsRetirals.Employee_Share_Amount, Employer_Share_Amount: npsRetirals.Employer_Share_Amount };
            } else {
                // Calculate percentage-based NPS contributions
                const employeeShareAmount = getRoundOffValue(formId.npsId, (npsRetirals.Employee_Share_Percentage * npsWages) / 100,roundOffSettings);
                const employerShareAmount = getRoundOffValue(formId.npsId, (npsRetirals.Employer_Share_Percentage * npsWages) / 100,roundOffSettings);
                npsResults = { Employee_Share_Amount: employeeShareAmount, Employer_Share_Amount: employerShareAmount };
            }

        }
        if (npsResults) {
            return {
                'Retirals_Id': npsRetirals.Retirals_Id,
                'Form_Id': npsRetirals.Form_Id,
                'Employee_Retiral_Wages': npsWages,
                'Employer_Retiral_Wages': npsWages,
                'Employee_Share_Amount': npsResults.Employee_Share_Amount,
                'Employer_Share_Amount': npsResults.Employer_Share_Amount,
                'Employee_Share_Percentage': npsRetirals.Employee_Share_Percentage,
                'Employer_Share_Percentage': npsRetirals.Employer_Share_Percentage,
                'Retiral_Type': npsRetirals?.Retirals_Type
            };
        }

        return null;
    } catch (error) {
        console.error('Error in calculateNPSDetails:', error);
        throw error;
    }
}

function calculateSlabWiseNps(npsSlabDetails, employeeNpsWage) {
    try {
        // Round off the employee's wage for NPS calculation
        employeeNpsWage = getRoundOffValue(formId.npsId, employeeNpsWage);
        let npsDetails = [];

        for (let npsSlab of npsSlabDetails) {
            // If Range_To is empty, set it to a very high number
            const rangeTo = npsSlab.Range_To || 9999999999999.99;

            if (npsSlab.Range_From <= employeeNpsWage && rangeTo >= employeeNpsWage) {
                const { Hdmf_Salary_Limit, Capped_Value, Employee_Share, Employer_Share } = npsSlab;
                let employeeShareAmount, employerShareAmount;

                if (Hdmf_Salary_Limit?.toLowerCase() === 'actual') {
                    employeeShareAmount = (employeeNpsWage * Employee_Share) / 100;
                    employerShareAmount = (employeeNpsWage * Employer_Share) / 100;
                } else {
                    employeeShareAmount = (Capped_Value * Employee_Share) / 100;
                    employerShareAmount = (Capped_Value * Employer_Share) / 100;
                }

                // Round off the amounts and return
                npsDetails = {
                    Employee_Share_Amount: getRoundOffValue(formId.npsId, employeeShareAmount),
                    Employer_Share_Amount: getRoundOffValue(formId.npsId, employerShareAmount)
                };

                return npsDetails;
            }
        }

        return npsDetails;
    } catch (error) {
        console.error('Error in calculateSlabWiseNps:', error);
        throw error;
    }
}

/**
 * Calculates statutory salary including basic pay and applicable allowances
 * @param {Array} allowanceDetails - Array of allowance details
 * @param {number} basicPay - Employee's basic pay
 * @param {string} benefitFormId - Benefit form ID to filter allowances
 * @param {number} adhocAllowanceAmount - Additional adhoc allowance amount (default: 0)
 * @returns {number} - Total statutory salary including applicable allowances
 */
function getStatutorySalary(allowanceDetails, basicPay, benefitFormId, adhocAllowanceAmount = 0) {
    try {
        let nonBonusAllowanceDetails = allowanceDetails.filter((item) => item.Allowance_Mode?.toLowerCase() === 'non bonus');

        let basicPlusAllowance = basicPay + (parseFloat(adhocAllowanceAmount) || 0);

        if (nonBonusAllowanceDetails && nonBonusAllowanceDetails.length > 0) {
            for (const allowance of nonBonusAllowanceDetails) {
                // Check if BenefitForms exists and contains the benefitFormId
                if (allowance.BenefitForms &&
                    allowance.BenefitForms.split(',').includes(benefitFormId)) {
                    basicPlusAllowance += parseFloat(allowance.Amount || 0);
                }
            }
        }

        return getRoundOffValue(formId.salary, basicPlusAllowance);
    } catch (error) {
        console.error('Error in getStatutorySalary:', error);
        throw error;
    }
}

function checkForRuntimeComponents(formula) {
    const RUNTIME_ONLY_COMPONENTS = [
        'paid_days_in_month',      // Depends on all retiral calculations
        'actual_days_in_month',      // Depends on retiral deductions
        'lop_days_in_month'                // Depends on final gross calculation
    ];

    const found = [];
    const formulaLower = formula.toLowerCase();

    RUNTIME_ONLY_COMPONENTS.forEach(comp => {
        // Use word boundary regex to match exact component names
        const regex = new RegExp(`\\b${comp}\\b`, 'gi');
        if (regex.test(formulaLower)) {
            found.push(comp);
        }
    });

    return found;
}

/**
 * Retrieves employee component values in bulk for multiple employees
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} employeeIds - Array of employee IDs
 * @param {string} payoutMonth - Payout month in 'M,YYYY' format
 * @param {Object} context - Context object with orgCode
 * @returns {Promise<Map>} - Map of employee ID to component values
 */
async function getEmployeeComponentValuesInBulk(organizationDbConnection, employeeIds, payoutMonth, context) {
    try {
        const allComponentsMap = new Map();
        const [month, year] = payoutMonth.split(',').map(s => s.trim());

        // Get salary dates first (required for dynamic components)
        const orgCode = context.orgCode;
        const { Salary_Date, Last_SalaryDate } = await commonLib.func.getSalaryDay(
            orgCode,
            organizationDbConnection,
            month,
            year
        );

        // ==========================================
        // PARALLEL EXECUTION: All independent queries
        // ==========================================
        
        const [
            allowances,
            grossComponents,
            salaryDetails,
            unpaidLeaveDaysMap,
            workScheduleData
        ] = await Promise.all([
            // Query 1: Get EARNING components (allowances)
            organizationDbConnection('employee_salary_allowance as ESA')
                .join('allowance_type as AT', 'AT.Allowance_Type_Id', 'ESA.Allowance_Type_Id')
                .join('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
                .select('SC.Component_Code', 'ESA.Amount', 'ESA.Employee_Id')
                .whereIn('ESA.Employee_Id', employeeIds)
                .where('SC.Component_Type', 'EARNING'),

            // Query 2: Get GROSS components
            organizationDbConnection('salary_gross_components as SGC')
                .join('gross_configuration as GC', 'GC.Gross_Id', 'SGC.Gross_Id')
                .join('salary_components as SC', 'GC.Salary_Component_Id', 'SC.Component_Id')
                .select('SC.Component_Code', 'SGC.Amount', 'SGC.Employee_Id')
                .whereIn('SGC.Employee_Id', employeeIds)
                .where('SC.Component_Type', 'GROSS'),

            // Query 3: Get CTC and salary details
            organizationDbConnection('employee_salary_details as ESD')
                .select('ESD.Employee_Id', 'ESD.Annual_Ctc', 'ESD.Annual_Gross_Salary', 'ESD.Monthly_Gross_Salary')
                .whereIn('ESD.Employee_Id', employeeIds),

            // Query 4: Get unpaid leave days for all employees
            commonLib.shiftAndTimeManagement.getUnpaidLeaveDaysForEmployees(
                organizationDbConnection,
                employeeIds,
                Salary_Date,
                Last_SalaryDate,
                payoutMonth
            ),
            // Query 5: Get work schedule data for all employees
            commonLib.shiftAndTimeManagement.prepareAttendanceInputs(
                organizationDbConnection,
                employeeIds,
                Salary_Date,
                Last_SalaryDate,
                true
            )
        ]);
        let employeesWorkScheduleDetails = workScheduleData.employeesWorkScheduleDetails
        const paidLeaveDaysMap = await getPaidLeaveDetailsMap(organizationDbConnection, employeeIds, Salary_Date, Last_SalaryDate,employeesWorkScheduleDetails);

        // ==========================================
        // BUILD REGULAR COMPONENTS MAP
        // ==========================================
        
        const regularComponentsMap = new Map();
        
        // Initialize with empty objects for all employees
        employeeIds.forEach(empId => {
            regularComponentsMap.set(empId, {});
        });

        // Add allowance components
        allowances.forEach(item => {
            const empComponents = regularComponentsMap.get(item.Employee_Id) || {};
            empComponents[item.Component_Code.toLowerCase()] = parseFloat(item.Amount) || 0;
            regularComponentsMap.set(item.Employee_Id, empComponents);
        });

        // Add gross components
        grossComponents.forEach(item => {
            const empComponents = regularComponentsMap.get(item.Employee_Id) || {};
            empComponents[item.Component_Code.toLowerCase()] = parseFloat(item.Amount) || 0;
            regularComponentsMap.set(item.Employee_Id, empComponents);
        });

        // Add CTC and salary detail components
        salaryDetails.forEach(detail => {
            const empComponents = regularComponentsMap.get(detail.Employee_Id) || {};
            empComponents['cost_to_company_amount'] = parseFloat(detail.Annual_Ctc) || 0;
            empComponents['annual_gross_salary'] = parseFloat(detail.Annual_Gross_Salary) || 0;
            empComponents['monthly_gross_salary'] = parseFloat(detail.Monthly_Gross_Salary) || 0;
            regularComponentsMap.set(detail.Employee_Id, empComponents);
        });

        // ==========================================
        // CALCULATE DYNAMIC COMPONENTS
        // ==========================================

        // 1. Calculate actual_days_in_month (number of days in the given month/year)
        const momentDate = moment(`${year}-${month}`, 'YYYY-M');
        const actualDaysInMonth = momentDate.daysInMonth();
        // 2. Extract work schedule details
        const employeeWorkScheduleShiftDetails = {
            employeeDetails: workScheduleData.employeeDetails,
            allWorkScheduleDetails: workScheduleData.allWorkScheduleDetails,
            allEmployeeShiftDetails: workScheduleData.allEmployeeShiftDetails,
            allHolidayDetails: workScheduleData.allHolidayDetails
        };

        // ==========================================
        // PART 3: MERGE COMPONENTS FOR EACH EMPLOYEE
        // ==========================================

        // Process each employee
        for (const employeeId of employeeIds) {
            try {
                // Get regular components for this employee
                const regularComponents = regularComponentsMap.get(employeeId) || {};

                // Get lop days for this employee
                const lopDaysInMonth = unpaidLeaveDaysMap.get(employeeId) || 0;

                // Get working days for this employee
                let workedDaysForTheEmployee = await commonLib.shiftAndTimeManagement.getBusinessWorkingDays(
                    Salary_Date,
                    Last_SalaryDate,
                    employeeId,
                    null,  // totalWorkingDays
                    null,  // leaveCalculationDays
                    null,  // formName
                    null,  // compOffCalculationMethod
                    employeeWorkScheduleShiftDetails
                );

                workedDaysForTheEmployee = workedDaysForTheEmployee?.days || 0;
                const paidDaysInMonth = workedDaysForTheEmployee - lopDaysInMonth;

                // Merge both regular and dynamic components
                const mergedComponents = {
                    ...regularComponents,
                    actual_days_in_month: actualDaysInMonth,
                    lop_days_in_month: lopDaysInMonth,
                    paid_days_in_month: paidDaysInMonth
                };

                allComponentsMap.set(employeeId, mergedComponents);
            } catch (err) {
                console.error(`Error processing components for employee ${employeeId}:`, err);
                // Set at least the regular components on error
                allComponentsMap.set(employeeId, regularComponentsMap.get(employeeId) || {});
            }
        }

        return allComponentsMap;
    } catch (error) {
        console.error('Error in getEmployeeComponentValuesInBulk:', error);
        throw error;
    }
}

async function getPaidLeaveDetailsMap(organizationDbConnection, employeeIds, Salary_Date, Last_SalaryDate, employeesWorkScheduleDetails) {
    console.log('getPaidLeaveDetailsMap', employeesWorkScheduleDetails[0], employeesWorkScheduleDetails[1]);

    const resultMap = new Map();

    // Get leave details for the period
    const leaveDetails = await commonLib.shiftAndTimeManagement.getAllLeaveDetails(organizationDbConnection, employeeIds, Salary_Date, Last_SalaryDate, ['Approved']);

    // Initialize all employees with 0
    employeeIds.forEach(empId => resultMap.set(empId, 0));

    // Loop through work schedule details
    employeesWorkScheduleDetails.forEach(scheduleDetail => {
        const employeeId = scheduleDetail.Employee_Id;
        const workScheduleDate = scheduleDetail.workScheduleDate;
        const workScheduleDetails = scheduleDetail.workScheduleDetails;

        // Check if this is a business working day
        if (workScheduleDetails && workScheduleDetails.Business_Working_Days > 0) {
            let timeOffDuration = 0;
            const startDate = new Date(workScheduleDate);

            // Check if this date falls within any leave period
            if (leaveDetails.length > 0) {
                leaveDetails.forEach(leave => {
                    if (leave.Employee_Id === employeeId) {
                        const leaveStartDate = new Date(leave.Start_Date);
                        const leaveEndDate = new Date(leave.End_Date);

                        if (startDate >= leaveStartDate && startDate <= leaveEndDate) {
                            if (leave.LeaveType_Id && leave.Duration) {
                                timeOffDuration += parseFloat(leave.Duration || 0);
                            }
                        }
                    }
                });
            }

            // Calculate working duration for this date (1 - leave duration, minimum 0)
            const workingDuration = Math.max(0, 1 - timeOffDuration);

            // Add to employee's total working days
            const currentTotal = resultMap.get(employeeId) || 0;
            resultMap.set(employeeId, currentTotal + workingDuration);
        }
    });

    console.log('resultMap', resultMap);

    return resultMap;
}