const { ehrTables } = require('../common/tablealias');
const AppConstant = require('./AppConstants');
const moment = require('moment-timezone');
const { organizeData } = require('./CommonFunctions');

async function calculateWorkScheduleWeekOffDates(organizationDbConnection, startDate, endDate) {
  try {
    const workScheduleWeekOff = await organizationDbConnection
      .select('WO.WorkSchedule_Id', 'WO.Week_Number', 'WO.Duration', 'WO.Day_Id', 'WO.Exclude_Last_Week', 'WD.Day_Name')
      .from({ WO: ehrTables.workScheduleWeekoff })
      .innerJoin({ WD: ehrTables.weekdays }, 'WD.Day_Id', 'WO.Day_Id');

    let weekendInfo = [];

    for (const data of workScheduleWeekOff) {
      const {
        WorkSchedule_Id: workScheduleId,
        Week_Number: weekNumber,
        Duration: duration,
        Day_Id: dayId,
        Day_Name: dayName,
        Exclude_Last_Week: excludeLastWeek
      } = data;


      let firstDate = moment(startDate);
      let lastDate = moment(endDate);
      while (firstDate <= lastDate) {
        // Get the week number of the current date
        const currentWeekNumber = weekOfMonth(firstDate);

        // Get the day of the week (1 - Monday, 7 - Sunday)
        let currentDayNo = moment(firstDate).day();
        if (Number(currentDayNo) === 0) {
          currentDayNo = 7;
        }

        // Check if current week is 4th or 5th and excludeLastWeek is 'Yes'
        if ([4, 5].includes(currentWeekNumber) && excludeLastWeek === 'Yes') {
          // Calculate the date of next week
          const nextWeekDate = moment(firstDate).add(7, 'days');

          // Get the week number of the next week
          const nextWeekNo = weekOfMonth(nextWeekDate);

          // Check if next week is not the 5th week of the month
          if (nextWeekNo === 5) {
            // Check if the current day matches the specified dayId and weekNumber
            if (currentWeekNumber === weekNumber && currentDayNo === dayId) {
              const weekendDate = moment(firstDate).format('YYYY-MM-DD');

              // Create an object with weekend date information and add it to weekendInfo
              const weekendDates = {
                WorkSchedule_Id: workScheduleId,
                Week_Number: weekNumber,
                Duration: duration,
                Day_Id: dayId,
                Day_Name: dayName,
                Weekend_Date: weekendDate,
              };
              weekendInfo.push(weekendDates);
            }
          }
        } else {
          // Check if the current day matches the specified dayId and weekNumber
          if (currentWeekNumber === weekNumber && currentDayNo === dayId) {
            const weekendDate = moment(firstDate).format('YYYY-MM-DD');

            // Create an object with weekend date information and add it to weekendInfo
            const weekendDates = {
              WorkSchedule_Id: workScheduleId,
              Week_Number: weekNumber,
              Duration: duration,
              Day_Id: dayId,
              Day_Name: dayName,
              Weekend_Date: weekendDate,
            };
            weekendInfo.push(weekendDates);
          }
        }

        // Convert firstDate to moment and move to the next day
        firstDate = moment(firstDate).add(1, 'day').toDate();
      }
    }

    const weekendInfoByWorkSchedule = {};

    for (const weekend of weekendInfo) {
      const weekendDate = weekend.Weekend_Date;
      const workScheduleId = weekend.WorkSchedule_Id;
      if (!weekendInfoByWorkSchedule[weekendDate]) {
        weekendInfoByWorkSchedule[weekendDate] = {};
      }
      weekendInfoByWorkSchedule[weekendDate][workScheduleId] = weekend;
    }

    return weekendInfoByWorkSchedule;
  } catch (workScheduleMainError) {
    console.log('Error in the calculateWorkScheduleWeekOffDates() function main catch block.', workScheduleMainError);
    throw workScheduleMainError;
  }
};

/**
 * Get the week number for a given date within its month.
 * @param {Date} date - The date for which to determine the week number.
 * @returns {number} - The week number (1-5) of the given date within its month.
 */
function weekOfMonth(date) {
  try {
    // Ensure date is a Date object
    if (!(date instanceof Date)) {
      date = new Date(date);
    }

    // Set the first day of the month
    const firstOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    // Calculate the number of days between the given date and the first of the month
    const dayLen = 86400000; // Number of milliseconds in a day
    const elapsed = Math.floor((date - firstOfMonth) / dayLen);

    // Determine the week number
    if (elapsed >= 0 && elapsed < 7) {
      return 1;
    } else if (elapsed >= 7 && elapsed < 14) {
      return 2;
    } else if (elapsed >= 14 && elapsed < 21) {
      return 3;
    } else if (elapsed >= 21 && elapsed < 28) {
      return 4;
    } else {
      return 5;
    }
  } catch (weekOfMonthMainError) {
    console.log('Error in the weekOfMonth() function main catch block.', weekOfMonthMainError);
    throw weekOfMonthMainError;
  }
};

function getLunchBreakHours(fromDateTime,endDateTime) {
  try {
    const regularFrom = moment(fromDateTime, 'YYYY-MM-DD HH:mm:ss');
    const regularTo = moment(endDateTime, 'YYYY-MM-DD HH:mm:ss');

    const interval = moment.duration(regularTo.diff(regularFrom));
    const hours = interval.hours();
    const minutes = interval.minutes();

    let hoursToMinutes = hours > 0 ? hours * 60 : 0;
    const midTime = Math.ceil((hoursToMinutes + minutes) / 2);

    const lunchBreakFrom = moment(regularFrom).add(midTime, 'minutes').format('YYYY-MM-DD HH:mm:ss');
    const lunchBreakEndMinutes = 1;
    const lunchBreakTo = moment(lunchBreakFrom, 'YYYY-MM-DD HH:mm:ss').add(lunchBreakEndMinutes, 'minutes').format('YYYY-MM-DD HH:mm:ss');

    const lunchBreakDetails = {
      Lunch_Break_From: lunchBreakFrom,
      Lunch_Break_To: lunchBreakTo
    };

    return lunchBreakDetails;
  } catch (hoursCalcMainError) {
    console.log('Error in the getLunchBreakHours() function main catch block.', hoursCalcMainError);
    throw hoursCalcMainError;
  }
}

async function getAllWorkScheduleDetails(db, shiftStartDate, shiftEndDate) {
  try {
    //Fetch all work schedule details from the database
    const workScheduleDetails = await db.select()
      .from({ WS: ehrTables.workSchedule }, [
        'WS.Check_In_Grace_Time', 'WS.Grace_Time_Flag', 'WS.Early_Check_In_Override',
        'WS.Check_Out_Time_Buffer', 'WS.WorkSchedule_Id', 'WS.Twodays_Flag',
        'WS.Check_In_Consideration_Time', 'WS.Check_Out_Consideration_Time',
        'WS.Regular_Work_Start_Time', 'WS.Regular_Work_End_Time',
        'WS.OverTime_Cooling_Period', 'WS.Allow_Attendance_Outside_Regular_WorkHours', 'WS.Title',
        'WS.Break_Schedule_Start_Time','WS.Break_Schedule_End_Time'
      ]);

    //Calculate work schedule week off dates
    const workScheduleWeekOffDates = await calculateWorkScheduleWeekOffDates(db, shiftStartDate, shiftEndDate);
    let shiftDetails = [];

    //Loop through work schedule details
    workScheduleDetails.forEach(workScheduleResult => {
      let current = moment(shiftStartDate).format('YYYY-MM-DD');
      const last = moment(shiftEndDate).format('YYYY-MM-DD');
      while (moment(current) <= moment(last)) {
        let startDate = current;
        let workSchedule = { ...workScheduleResult };//Assign this result to another variable in order to avoid duplicate results after pushing it result array

        // Set 'Regular_From' and 'Regular_To'
        workSchedule.Regular_From = moment(startDate + ' ' + workSchedule.Regular_Work_Start_Time, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss');
        workSchedule.Regular_To = moment(startDate + ' ' + workSchedule.Regular_Work_End_Time, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss');

        // Adjust 'Regular_To' if 'Twodays_Flag' is set
        if (workSchedule.Twodays_Flag === 1) {
          workSchedule.Regular_To = moment(workSchedule.Regular_To, 'YYYY-MM-DD HH:mm:ss').add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
        }

        // Set 'Consideration_From'
        if (workSchedule.Check_In_Consideration_Time > 0) {
          workSchedule.Consideration_From = moment(workSchedule.Regular_From, 'YYYY-MM-DD HH:mm:ss').subtract(workSchedule.Check_In_Consideration_Time, 'minutes').format('YYYY-MM-DD HH:mm:ss');
        } else {
          workSchedule.Consideration_From = workSchedule.Regular_From;
        }

        // Set 'Consideration_To'
        if (workSchedule.Check_Out_Consideration_Time > 0) {
          workSchedule.Consideration_To = moment(workSchedule.Regular_To, 'YYYY-MM-DD HH:mm:ss').add(workSchedule.Check_Out_Consideration_Time, 'minutes').format('YYYY-MM-DD HH:mm:ss');
        } else {
          workSchedule.Consideration_To = workSchedule.Regular_To;
        }

        // Set 'Work_Schedule_Date'
        workSchedule.Work_Schedule_Date = moment(workSchedule.Regular_From, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD');
      
        // Set 'Break_Hours_From' and 'Break_Hours_To'
        if(workSchedule.Break_Schedule_Start_Time && (workSchedule.Break_Schedule_Start_Time !== '00:00:00'
        || (workSchedule.Break_Schedule_Start_Time == '00:00:00' && workSchedule.Break_Schedule_End_Time !== '00:00:00'))){
          let workScheduleBreakHoursFrom = moment(startDate + ' ' + workSchedule.Break_Schedule_Start_Time, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss');

          if( workSchedule.Twodays_Flag === 1){
            if(moment(workSchedule.Regular_From)<=moment(workScheduleBreakHoursFrom) && moment(workSchedule.Regular_To)>=moment(workScheduleBreakHoursFrom)){
              workScheduleBreakHoursFrom = workScheduleBreakHoursFrom;
            }else{
              workScheduleBreakHoursFrom = moment(startDate + ' ' + workSchedule.Break_Schedule_Start_Time, 'YYYY-MM-DD HH:mm:ss').add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            }
          }
          workSchedule.Break_Hours_From = workScheduleBreakHoursFrom;
        }else{
          workSchedule.Break_Hours_From = '';
        }

        if(workSchedule.Break_Schedule_End_Time && (workSchedule.Break_Schedule_End_Time !== '00:00:00'
          || (workSchedule.Break_Schedule_End_Time == '00:00:00' && workSchedule.Break_Schedule_Start_Time !== '00:00:00'))){

          let workScheduleBreakHoursTo = moment(startDate + ' ' + workSchedule.Break_Schedule_End_Time, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss');

          if( workSchedule.Twodays_Flag === 1){
            if(moment(workSchedule.Regular_From)<=moment(workScheduleBreakHoursTo) && moment(workSchedule.Regular_To)>=moment(workScheduleBreakHoursTo)){
              workScheduleBreakHoursTo = workScheduleBreakHoursTo;
            }else{
              workScheduleBreakHoursTo = moment(startDate + ' ' + workSchedule.Break_Schedule_End_Time, 'YYYY-MM-DD HH:mm:ss').add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            }
          }
          workSchedule.Break_Hours_To = workScheduleBreakHoursTo;

        }else{
          workSchedule.Break_Hours_To = '';
        }
        
        const workScheduleId = workSchedule.WorkSchedule_Id;
        const workScheduleWeekOff = workScheduleWeekOffDates[startDate] && workScheduleWeekOffDates[startDate][workScheduleId] ? workScheduleWeekOffDates[startDate][workScheduleId] : null;

        if (workScheduleWeekOff !== null) {
          workSchedule.Week_Off_Exist = 'Yes';
          workSchedule.Week_Off_Duration = workScheduleWeekOff.Duration;
        } else {
          workSchedule.Week_Off_Exist = 'No';
          workSchedule.Week_Off_Duration = 0;
        }

        if(workSchedule.Break_Hours_From && workSchedule.Break_Hours_To ){
          workSchedule.Lunch_Break_From = workSchedule.Break_Hours_From;
          workSchedule.Lunch_Break_To = workSchedule.Break_Hours_To;
        }else{
          // Calculate lunch break details
          const lunchBreakDetails = getLunchBreakHours(workSchedule.Regular_From,workSchedule.Regular_To);
          workSchedule.Lunch_Break_From = lunchBreakDetails.Lunch_Break_From;
          workSchedule.Lunch_Break_To = lunchBreakDetails.Lunch_Break_To;
        }

        let firstHalfMidWorkTimeResult = getLunchBreakHours(workSchedule.Regular_From,workSchedule.Lunch_Break_From)
        workSchedule.First_Half_Break_From = firstHalfMidWorkTimeResult.Lunch_Break_From;
        workSchedule.First_Half_Break_To = firstHalfMidWorkTimeResult.Lunch_Break_To;

        let secondHalfMidWorkTimeResult = getLunchBreakHours(workSchedule.Lunch_Break_To,workSchedule.Regular_To);
        workSchedule.Second_Half_Break_From = secondHalfMidWorkTimeResult.Lunch_Break_From;
        workSchedule.Second_Half_Break_To = secondHalfMidWorkTimeResult.Lunch_Break_To;

        workSchedule.Full_Day_From = workSchedule.Regular_From;
        workSchedule.Full_Day_To = workSchedule.Regular_To;

        workSchedule.First_Half_From = workSchedule.Regular_From;
        workSchedule.First_Half_To = workSchedule.Lunch_Break_From;

        workSchedule.Second_Half_From = workSchedule.Lunch_Break_To;
        workSchedule.Second_Half_To = workSchedule.Regular_To;

        workSchedule.First_Quarter_From = workSchedule.Regular_From;
        workSchedule.First_Quarter_To = workSchedule.First_Half_Break_From;
        
        workSchedule.Second_Quarter_From = workSchedule.First_Half_Break_To,
        workSchedule.Second_Quarter_To = workSchedule.Lunch_Break_From;

        workSchedule.Third_Quarter_From =workSchedule.Lunch_Break_To;
        workSchedule.Third_Quarter_To = workSchedule.Second_Half_Break_From;

        workSchedule.Fourth_Quarter_From = workSchedule.Second_Half_Break_To;
        workSchedule.Fourth_Quarter_To = workSchedule.Regular_To;

        shiftDetails.push(workSchedule);

        // Move to the next date
        current = moment(current).add(1, 'days').format('YYYY-MM-DD');
      }
    });

    const shiftDetailsByDateAndEmployee = {};

    //Organize shift details by date and work schedule ID
    shiftDetails.forEach(shiftDetail => {
      const workScheduleDate = shiftDetail.Work_Schedule_Date;
      const workScheduleId = shiftDetail.WorkSchedule_Id;
      if (!shiftDetailsByDateAndEmployee[workScheduleId]) {
        shiftDetailsByDateAndEmployee[workScheduleId] = {};
      }
      shiftDetailsByDateAndEmployee[workScheduleId][workScheduleDate] = shiftDetail;
    });

    return shiftDetailsByDateAndEmployee;
  } catch (workScheduleMainError) {
    console.log('Error in the getAllWorkScheduleDetails() function main catch block.', workScheduleMainError);
    throw workScheduleMainError;
  }
}

async function getEmployeeDetails(db, employeeIds, startDate, endDate) {
  try {
    // CHANGED: Reading Work_Schedule_Level from emp_job instead of employee_type
    let employeeDetails = await db.select('EJ.Employee_Id', 'ET.Salary_Calc_Days', 'ET.Fixed_Days',
      'ET.Comp_Off_Days', 'ET.Comp_Off_Fixed_Days',
      'EJ.Work_Schedule_Level as Work_Schedule', 'EJ.Work_Schedule as WorkSchedule_Id', 'EJ.Date_Of_Join', 'R.Resignation_Date',
      'EJ.Manager_Id','ECD.Mobile_No','TH.Regular_Hours','R.Notice_Date','EJ.Probation_Date','EJ.Confirmation_Date')
      .from({ EJ: ehrTables.empJob })
      .leftJoin({ R: ehrTables.resignation }, function () {
        this.on('EJ.Employee_Id', '=', 'R.Employee_Id')
          .onIn('R.Approval_Status', db.raw('(?,?)', ['Applied', 'Approved']))
      })
      .leftJoin({ ECD: ehrTables.contactDetails }, 'EJ.Employee_Id', 'ECD.Employee_Id')
      .leftJoin({ DES: ehrTables.designation }, 'EJ.Designation_Id', 'DES.Designation_Id')
      .leftJoin({ TH: ehrTables.timesheetHours }, 'EJ.Grade_Id', 'TH.Grade_Id')
      .leftJoin({ ET: ehrTables.employeeType }, 'EJ.EmpType_Id', 'ET.EmpType_Id')
      .whereIn('EJ.Employee_Id', employeeIds);

       // Merge basic employee details with salary configuration
       if(startDate && endDate){
      employeeDetails = employeeDetails.map(emp => {
      // Calculate Salary_Date and Last_Salary_Date based on Date_Of_Join and Resignation_Date
      let empSalaryDate = startDate;
      let empLastSalaryDate = endDate;

      // If employee joined after the salary period start date, use Date_Of_Join
      if (emp.Date_Of_Join && moment(emp.Date_Of_Join).isAfter(moment(startDate))) {
        empSalaryDate = emp.Date_Of_Join;
      }

      // If employee resigned before the salary period end date, use Resignation_Date
      if (emp.Resignation_Date && moment(emp.Resignation_Date).isBefore(moment(endDate))) {
        empLastSalaryDate = emp.Resignation_Date;
      }
       return {
        ...emp,
        Salary_Date: empSalaryDate,
        Last_Salary_Date: empLastSalaryDate
      };
      
    });
    }

    return employeeDetails;
  } catch (empMainError) {
    console.log('Error in the getEmployeeDetails() function main catch block.', empMainError);
    throw empMainError;
  }
}

async function getAllEmployeeShiftDetails(db, employeeDetails, salaryDate, lastSalaryDate) {
  try {
    //Initialize arrays to hold shift roster employee IDs and shift details
    const shiftRoasterEmployeeIds = [];
    const shiftDetailsByDateAndEmployee = {};

    //Loop through employee details to collect employees with 'Shift Roster'
    employeeDetails.forEach(employeeData => {
      if (employeeData.Work_Schedule?.toLowerCase() === 'shift roster') {
        shiftRoasterEmployeeIds.push(employeeData.Employee_Id);
      }
    });

    //If there are employees with shift rosters, fetch their shift details from the database
    if (shiftRoasterEmployeeIds.length > 0) {
      const allShiftDetails = await db.select()
        .from({ SEM: ehrTables.shiftEmpMapping }, ['SEM.Shift_Start_Date', 'SEM.Employee_Id', 'EST.WorkSchedule_Id', 'SEM.Week_Off'])
        .join({ EST: ehrTables.empShiftType }, 'EST.Shift_Type_Id', '=', 'SEM.Shift_Type_Id')
        .whereIn('SEM.Employee_Id', shiftRoasterEmployeeIds)
        .where('SEM.Shift_Start_Date', '>=', salaryDate)
        .where('SEM.Shift_Start_Date', '<=', lastSalaryDate)
        .orderBy(['SEM.Employee_Id', 'SEM.Shift_Start_Date']);
      // Organize the shift details by employee ID and shift start date
      allShiftDetails.forEach(shiftDetail => {
        const shiftStartDate = shiftDetail.Shift_Start_Date;
        const employeeId = shiftDetail.Employee_Id;

        if (!shiftDetailsByDateAndEmployee[employeeId]) {
          shiftDetailsByDateAndEmployee[employeeId] = {};
        }

        shiftDetailsByDateAndEmployee[employeeId][shiftStartDate] = shiftDetail;
      });
    } else {
      console.log('Empty shift roster employee ids', shiftRoasterEmployeeIds)
    }

    // Return the organized shift details
    return shiftDetailsByDateAndEmployee;
  } catch (empShiftMainError) {
    console.log('Error in the getAllEmployeeShiftDetails() function main catch block.', empShiftMainError);
    throw empShiftMainError;
  }
}

async function getAllHolidayDates(db, salaryStartDate, salaryEndDate, otherInputs={}) {
  try {
    let { listPersonalChoiceHoliday = 'no', employeeId = [] } = otherInputs;

    // Initialize objects to store holiday details
    const holidayDetailsByDateAndEmployee = {};

    // Fetch holiday coverage type from the database
    const holidayCoverage = await db
      .select('Holiday_Settings_Type as Holiday_Settings')
      .from(ehrTables.holidaySettings)
      .first();

    let orgHolidayDatesQry;
    // Build the query based on holiday coverage type
    if (holidayCoverage.Holiday_Settings === 'CUSTOMGROUP') {
      const formId = AppConstant.formIdHolidays;
      const customGroupEmployeesType = ['Default', 'AdditionalInclusion'];

      orgHolidayDatesQry = db
        .select([
          'H.Holiday_Date',
          'EJ.Employee_Id',
          'H.Mandatory',
          'H.Personal_Choice',
          'H.Holiday',
          'HT.Holiday_Name',
        ])
        .from({ H: ehrTables.holidayCustomGroupAssignment })
        .innerJoin({ HT: ehrTables.holiday }, 'HT.Holiday_Id', 'H.Holiday_Id')
        .innerJoin({ CGAF: ehrTables.cusGroupAssocitedForms }, 'CGAF.Parent_Id', 'H.Holiday_Assign_Id')
        .innerJoin({ CEGE: ehrTables.customEmployeeGroupEmployees }, 'CGAF.Custom_Group_Id', 'CEGE.Group_Id')
        .innerJoin({ EJ: ehrTables.empJob }, 'EJ.Employee_Id', 'CEGE.Employee_Id')
        .innerJoin({ ET: ehrTables.employeeType }, 'EJ.EmpType_Id', 'ET.EmpType_Id')
        .where('ET.Holiday_Eligiblity', '1')
        .where(function () {
          this.where('H.Mandatory', 1).orWhere('H.Holiday', 1);
        })
        .where('H.Holiday_Date', '>=', salaryStartDate)
        .where('H.Holiday_Date', '<=', salaryEndDate)
        .where('CGAF.Form_Id', formId)
        .whereIn('CEGE.Type', customGroupEmployeesType)
        .groupBy('H.Holiday_Date', 'EJ.Employee_Id');
    } else {
      orgHolidayDatesQry =  db
        .select([
          'H.Holiday_Date',
          'EJ.Employee_Id',
          'H.Mandatory',
          'H.Personal_Choice',
          'H.Holiday',
        ])
        .from({ H: ehrTables.holidayAssignment })
        .innerJoin({ HT: ehrTables.holiday }, 'HT.Holiday_Id', 'H.Holiday_Id')
        .innerJoin({ EJ: ehrTables.empJob }, 'EJ.Location_Id', 'H.Location_Id')
        .innerJoin({ ET: ehrTables.employeeType }, 'EJ.EmpType_Id', 'ET.EmpType_Id')
        .where('ET.Holiday_Eligiblity', '1')
       
        .where('H.Holiday_Date', '>=', salaryStartDate)
        .where('H.Holiday_Date', '<=', salaryEndDate)
        .groupBy(['H.Holiday_Date', 'EJ.Employee_Id']);
    }

    if(listPersonalChoiceHoliday.toLowerCase() === 'yes'){
      orgHolidayDatesQry= orgHolidayDatesQry.where(function () {
        this.where('H.Personal_Choice', 1)
      })     
    }else{
      orgHolidayDatesQry= orgHolidayDatesQry.where(function () {
        this.where('H.Mandatory', 1).orWhere('H.Holiday', 1);
      });
    }

    if(employeeId?.length){
      orgHolidayDatesQry=orgHolidayDatesQry.whereIn('EJ.Employee_Id',employeeId);
    }
    
    // Fetch all holiday details
    const allHolidayDetails = await orgHolidayDatesQry;

    // Organize the holiday details by date and employee
    allHolidayDetails.forEach(holidayDetail => {
      const holidayDate = holidayDetail.Holiday_Date;
      const employeeId = holidayDetail.Employee_Id;

      if (!holidayDetailsByDateAndEmployee[employeeId]) {
        holidayDetailsByDateAndEmployee[employeeId] = {};
      }

      holidayDetailsByDateAndEmployee[employeeId][holidayDate] = holidayDetail;
    });

    // Return the organized holiday details
    return holidayDetailsByDateAndEmployee;
  } catch (holidayMainError) {
    console.log('Error in the getAllHolidayDates() function main catch block.', holidayMainError);
    throw holidayMainError;
  }
}

async function getRosterManagementSettings(db, employeeId = null) {
  try {
    let rosterManagementSettings;

    // If employeeId is provided (single employee), read all fields from attendance_policy via emp_job
    if ((employeeId && (Array.isArray(employeeId) && employeeId.length === 1)) || (!Array.isArray(employeeId) && employeeId)) {
      let employeeIdNum = Array.isArray(employeeId) ? employeeId[0]: employeeId;
      // Get all attendance policy fields for single employee (each employee has only one emp_job record)
      let attendancePolicyFields = await db(ehrTables.empJob + ' as EJ')
        .select('AP.Allow_Past_Shift_Swaps', 'AP.Max_Shift_Swap_Days',
                'AP.Enable_Shift_Swap_Restriction', 'AP.Max_Swap_Requests_Per_Month',
                'AP.Dynamic_Week_Off', 'AP.Overlap_Shift_Schedule')
        .leftJoin(ehrTables.attendancePolicy + ' as AP', 'AP.Attendance_Policy_Id', 'EJ.Attendance_Policy_Id')
        .where('EJ.Employee_Id', employeeIdNum)
        .first();

      // Return attendance policy fields with defaults if not found
      rosterManagementSettings = {
        Allow_Past_Shift_Swaps: attendancePolicyFields?.Allow_Past_Shift_Swaps || 'No',
        Max_Shift_Swap_Days: attendancePolicyFields?.Max_Shift_Swap_Days || 0,
        Enable_Shift_Swap_Restriction: attendancePolicyFields?.Enable_Shift_Swap_Restriction || 'No',
        Max_Swap_Requests_Per_Month: attendancePolicyFields?.Max_Swap_Requests_Per_Month || 0,
        Dynamic_Week_Off: attendancePolicyFields?.Dynamic_Week_Off || 0,
        Overlap_Shift_Schedule: attendancePolicyFields?.Overlap_Shift_Schedule || 0
      };

      return rosterManagementSettings;
    }

    // If multiple employeeIds are provided, fetch from attendance_policy and apply batch logic
    if (employeeId && Array.isArray(employeeId) && employeeId.length > 1) {
      const attendancePolicySettings = await db(ehrTables.empJob + ' as EJ')
        .select('AP.Dynamic_Week_Off', 'AP.Overlap_Shift_Schedule')
        .leftJoin(ehrTables.attendancePolicy + ' as AP', 'AP.Attendance_Policy_Id', 'EJ.Attendance_Policy_Id')
        .whereIn('EJ.Employee_Id', employeeId);

      // Apply logic: if ANY employee has the setting enabled (value = 1), use it for all
      const dynamicWeekOff = attendancePolicySettings.some(s => s.Dynamic_Week_Off === 1) ? 1 : 0;
      const overlapShiftSchedule = attendancePolicySettings.some(s => s.Overlap_Shift_Schedule === 1) ? 1 : 0;

      rosterManagementSettings = {
        Dynamic_Week_Off: dynamicWeekOff,
        Overlap_Shift_Schedule: overlapShiftSchedule
      };

      return rosterManagementSettings;
    }

    // Fallback: read from roster_management_settings table (for backward compatibility when no employeeId provided)
    rosterManagementSettings = await db
      .select('*')
      .from({ RMS: ehrTables.rosterManagementSettings })
      .then((settingsResult) => {
        return (settingsResult && settingsResult.length > 0) ? settingsResult[0] : []
      });

    return rosterManagementSettings;
  } catch (error) {
    console.error('Error in getRosterManagementSettings function main catch block.', error);
    throw error;
  }
}

async function getWorkScheduleDetailsByDateForShiftRosterEmployees(startDate, allShiftDetails, allWorkScheduleDetails, rosterManagementSettings) {
  try {
    let workScheduleDetails = {};
    const shiftDetails = allShiftDetails ? allShiftDetails[startDate] : null;

    if (shiftDetails) {
      const workScheduleId = shiftDetails?.WorkSchedule_Id;
      workScheduleDetails = allWorkScheduleDetails[workScheduleId]?.[startDate] || null;
      if (workScheduleDetails) {
        // Overlap_Shift_Schedule: Read from attendance_policy via emp_job
        if (rosterManagementSettings.Overlap_Shift_Schedule === 1) {
          workScheduleDetails = await getOverLapShiftSchedulingDetails(allShiftDetails, allWorkScheduleDetails, startDate, workScheduleDetails);
        }

        if (workScheduleDetails && Object.keys(workScheduleDetails).length > 0) {
          // Dynamic_Week_Off: Read from attendance_policy via emp_job
          if (rosterManagementSettings.Dynamic_Week_Off === 1) {
            if (shiftDetails.Week_Off) {
              workScheduleDetails.Week_Off_Exist = 'Yes';
              workScheduleDetails.Week_Off_Duration = 1;
            } else {
              workScheduleDetails.Week_Off_Exist = 'No';
              workScheduleDetails.Week_Off_Duration = 0;
            }
          }
        }
      }else {
        console.log('Empty workScheduleDetails in the getWorkScheduleDetailsByDateForShiftRosterEmployees function', allWorkScheduleDetails, workScheduleId,startDate);
      }
    } else {
      console.log('Empty shiftdetails in the getWorkScheduleDetailsByDateForShiftRosterEmployees function', shiftDetails,startDate);
    }
    return workScheduleDetails;
  } catch (error) {
    console.error('Error in getWorkScheduleDetailsByDateForShiftRosterEmployees function main catch block.', error,startDate);
    throw error;
  }
}

async function getOverLapShiftSchedulingDetails(allShiftDetails, allWorkScheduleDetails, startDate, graceTimeDetails) {
  try {
    // Convert startDate to JavaScript Date object and calculate the next day
    const nextDate = new Date(new Date(startDate).setDate(new Date(startDate).getDate() + 1));
    const formattedNextDate = nextDate.toISOString().split('T')[0]; // Format next date as "YYYY-MM-DD"

    const shiftDetails = allShiftDetails ? allShiftDetails[formattedNextDate] : null;

    if (shiftDetails !== null) {
      const workScheduleId = shiftDetails?.WorkSchedule_Id ?? 0;
      if (workScheduleId === 0) {
        console.log('Missing WorkSchedule_Id for shiftDetails on date:', formattedNextDate, shiftDetails);
        return null; // Exit early when WorkSchedule_Id is undefined
      }
      const nextShiftGraceTimeDetails = (allWorkScheduleDetails[workScheduleId] || {})[formattedNextDate] || null;

      if (nextShiftGraceTimeDetails !== null) {
        if (nextShiftGraceTimeDetails.Check_In_Consideration_Time > 0) {
          const considerationTimeInMinutes = nextShiftGraceTimeDetails.Check_In_Consideration_Time;
          const regularFrom = new Date(nextShiftGraceTimeDetails.Regular_From);
          nextShiftGraceTimeDetails.Consideration_From = new Date(regularFrom.getTime() - considerationTimeInMinutes * 60000).toISOString().replace('T', ' ').split('.')[0];
        } else {
          nextShiftGraceTimeDetails.Consideration_From = nextShiftGraceTimeDetails.Regular_From;
        }

        const nextShiftConsiderationTime = new Date(new Date(nextShiftGraceTimeDetails.Consideration_From).getTime() - 60000).toISOString().replace('T', ' ').split('.')[0];

        if (new Date(nextShiftConsiderationTime) > new Date(graceTimeDetails.Regular_To)) {
          const checkInConsiderationDateTime = new Date(graceTimeDetails.Consideration_From).getTime();
          const checkOutConsiderationDateTime = new Date(nextShiftConsiderationTime).getTime();

          if (checkOutConsiderationDateTime > checkInConsiderationDateTime) {
            const differenceInDateTime = checkOutConsiderationDateTime - checkInConsiderationDateTime;

            if (differenceInDateTime <= 86400000) { // 86400 seconds in milliseconds
              graceTimeDetails.Consideration_To = nextShiftConsiderationTime;
            }
          }
        } else {
          return ''; // No overlapping shifts
        }
      } else {
        return nextShiftGraceTimeDetails; // No next shift grace time details
      }

      return graceTimeDetails; // Return updated grace time details
    } else {
      console.log('Empty shiftdetails in the getOverLapShiftSchedulingDetails function', shiftDetails);
    }

    return null; // If shiftDetails is null, return null
  } catch (overlapShiftScheduleError) {
    console.log('Error in getOverLapShiftSchedulingDetails function main catch block.', overlapShiftScheduleError);
    throw overlapShiftScheduleError;
  }
}

/**
 * Check if a given date is a working day for an employee, considering holidays and work schedule.
 *
 * @param {string} startDate - The date to check if it's a working day.
 * @param {Object} holidayDetailsByDateAndEmployee - Details of holidays.
 * @param {Object} workScheduleDetails - Object to store the result of the working day check.
 *
 * @returns {Object} Updated workScheduleDetails object containing holiday and working day information.
*/
async function checkWorkingDay(startDate, holidayDetailsByDateAndEmployee, workScheduleDetails) {
  try {
    let businessWorkingDays = 0;
    if (Object.keys(workScheduleDetails).length > 0) {
      const holidayExist = holidayDetailsByDateAndEmployee?.[startDate] || null;

      // If a holiday exists, update workScheduleDetails with holiday details.
      if (holidayExist !== null) {
        workScheduleDetails.Holiday_Exist = 'Yes';
        workScheduleDetails.Mandatory = holidayExist.Mandatory;
        workScheduleDetails.Personal_Choice = holidayExist.Personal_Choice;
        workScheduleDetails.Holiday = holidayExist.Holiday;
      } else {
        // No holiday found, set default values.
        workScheduleDetails.Holiday_Exist = 'No';
        workScheduleDetails.Mandatory = 0;
        workScheduleDetails.Personal_Choice = 0;
        workScheduleDetails.Holiday = 0;
      }

      if (workScheduleDetails.Holiday_Exist === 'Yes') {
        // Holidays always result in 0 business working days
        workScheduleDetails.Business_Working_Days = 0;
      } else if (workScheduleDetails.Week_Off_Exist === 'Yes') {
        // For week offs, consider the duration: 0.5 for half-day, 1 for full day
        const weekOffDuration = workScheduleDetails.Week_Off_Duration || 0;
        if (weekOffDuration === 0.5) {
          // Half-day week off: 0.5 business working days
          businessWorkingDays = 0.5;
          workScheduleDetails.Business_Working_Days = businessWorkingDays;
        } else {
          // Full week off (duration 1 or any other value): 0 business working days
          workScheduleDetails.Business_Working_Days = 0;
        }
      } else {
        // Regular working day
        workScheduleDetails.Business_Working_Days = 1;
        businessWorkingDays = 1;
      }
    } else {
      console.log('Empty workschedule in the checkWorkingDay function', workScheduleDetails)
    }

    // Check if there's a holiday or week off, and update Business_Working_Days accordingly.
    return {
      Business_Working_Days: businessWorkingDays,
      Work_Schedule_Details: workScheduleDetails
    };
  } catch (workingDayMainError) {
    console.log('Error in checkWorkingDay function main catch block.', workingDayMainError);
    throw workingDayMainError;
  }
}
const createDayEntry = (date, workScheduleDetails = {}, employeeShiftDetails,empShiftType=null, businessWorkingDay) => {

  return {
      date,
      isWeekOffDay: workScheduleDetails?.Week_Off_Exist === 'Yes' ? 1 : 0,
      weekOffDuration: workScheduleDetails?.Week_Off_Duration || 0,
      isHoliday: workScheduleDetails?.Holiday_Exist === 'Yes' ? 1 : 0,
      isShiftScheduled: employeeShiftDetails ? 1 : empShiftType && empShiftType.toLowerCase() !== 'shift roster' ? 1 : 0,
      businessWorkingDay: businessWorkingDay
  };
};
const processEmployeeSchedule = async (
  startDate,
  endDate,
  employeeDetails,
  employeeShiftDetails,
  employeeWorkScheduleDetails,
  allWorkScheduleDetails,
  rosterManagementSettings,
  employeeHolidayDetails
) => {
  console.log('Inside processEmployeeSchedule function')
  try {
  let currentDate = startDate;
  let totalBusinessWorkingDays = 0;
  const weekOffAndHolidayDetails = [];
  let workScheduleToSend=[];
  while (currentDate <= endDate) {
      let workSchedule = null;

      if (employeeDetails?.Work_Schedule?.toLowerCase() === 'shift roster') {
          workSchedule = await getWorkScheduleDetailsByDateForShiftRosterEmployees(
              currentDate,
              employeeShiftDetails,
              allWorkScheduleDetails,
              rosterManagementSettings
          );
      } else {
          // Add null check for employeeWorkScheduleDetails
          workSchedule = employeeWorkScheduleDetails && employeeWorkScheduleDetails[currentDate] ? employeeWorkScheduleDetails[currentDate] : null;
      }
      if (workSchedule) { 
        if(Object.keys(workSchedule).length>0){
        workScheduleToSend.push({
          Work_Schedule_Date:workSchedule.Work_Schedule_Date,
          Consideration_From:workSchedule.Consideration_From,
          Consideration_To:workSchedule.Consideration_To,
          Regular_From:workSchedule.Regular_From,
          Regular_To:workSchedule.Regular_To,
          Allow_Attendance_Outside_Regular_WorkHours:workSchedule.Allow_Attendance_Outside_Regular_WorkHours,
        })
      }
          try {
              const checkWorkingDays = await checkWorkingDay(
                  currentDate,
                  employeeHolidayDetails,
                  workSchedule
              );
              const currentWorkScheduleDetails = checkWorkingDays?.Work_Schedule_Details || {};
              const businessWorkingDays = checkWorkingDays?.Business_Working_Days || 0;
              const employeeShiftDetailsAccordingToCurrentDate = employeeShiftDetails ? employeeShiftDetails[currentDate] : null;

              totalBusinessWorkingDays += businessWorkingDays;
              weekOffAndHolidayDetails.push(createDayEntry(currentDate, currentWorkScheduleDetails, employeeShiftDetailsAccordingToCurrentDate,employeeDetails?.Work_Schedule?.toLowerCase(),businessWorkingDays));
          } catch (error) {
              console.error('Error in fetchEmployeeWeekOffAndHolidayDetails checkWorkingDay block', error,currentDate,employeeHolidayDetails,workSchedule);
              // If error occurs, add a default entry
              weekOffAndHolidayDetails.push(createDayEntry(currentDate, {}, null,0));
          }
      } else {
          // If no work schedule, add a default entry
          weekOffAndHolidayDetails.push(createDayEntry(currentDate, {}, null,0));
      }

      currentDate = moment(currentDate).add(1, 'days').format('YYYY-MM-DD');
  }

  return { weekOffAndHolidayDetails, totalBusinessWorkingDays,workScheduleToSend };
  } catch (error) {
    console.error('Error in fetchEmployeeWeekOffAndHolidayDetails', error,employeeShiftDetails);
    throw error;
  }
};
const fetchEmployeeWeekOffAndHolidayDetails = async (
  organizationDbConnection,
  employeeId,
  startDate,
  endDate
) => {
  console.log('Inside fetchEmployeeWeekOffAndHolidayDetails function')
  try {
      let allEmployeeWorkScheduleDetails = {};
      // Convert employeeId to array for consistent processing
      const allEmployeeIds =  (Array.isArray(employeeId)) ? employeeId : [employeeId];

      // Get roster management settings from attendance_policy via emp_job for the employee(s)
      let rosterSettings = await getRosterManagementSettings(organizationDbConnection, allEmployeeIds);

      if (rosterSettings.Overlap_Shift_Schedule === 1){
        startDate = moment(startDate).subtract(1, 'days').format('YYYY-MM-DD');
        endDate = moment(endDate).add(1, 'days').format('YYYY-MM-DD');
      }
      // Fetch all required data in parallel
      const [
          allEmployeeDetails,
          allWorkScheduleDetails,
          rosterManagementSettings,
          allHolidayDetails,
      ] = await Promise.all([
        getEmployeeDetails(organizationDbConnection, allEmployeeIds),
        getAllWorkScheduleDetails(organizationDbConnection, startDate, endDate),
        getRosterManagementSettings(organizationDbConnection, allEmployeeIds),
        getAllHolidayDates(organizationDbConnection, startDate, endDate),
      ]);

      const allEmployeeShiftDetails = await getAllEmployeeShiftDetails(
          organizationDbConnection,
          allEmployeeDetails,
          startDate,
          endDate
      );
      
      const groupEmployeeDetails = organizeData(allEmployeeDetails, 'Employee_Id');
      const employeeShiftDetails = allEmployeeShiftDetails?.[employeeId] || null;
      const employeeHolidayDetails = allHolidayDetails?.[employeeId] || null;

      for (let inputEmployeeId of allEmployeeIds){
        let employeeDetails = groupEmployeeDetails[inputEmployeeId] ? groupEmployeeDetails[inputEmployeeId][0] : {};
        const workScheduleId = employeeDetails?.WorkSchedule_Id || null;

        let employeeWorkScheduleDetails = null;
        if (employeeDetails?.Work_Schedule?.toLowerCase() !== 'shift roster' && workScheduleId && allWorkScheduleDetails) {
          employeeWorkScheduleDetails = allWorkScheduleDetails[workScheduleId] || null;
        }

        // Process employee schedule data
        const { weekOffAndHolidayDetails, totalBusinessWorkingDays,workScheduleToSend } = await processEmployeeSchedule(
            startDate,
            endDate,
            employeeDetails,
            employeeShiftDetails,
            employeeWorkScheduleDetails,
            allWorkScheduleDetails,
            rosterManagementSettings,
            employeeHolidayDetails
        );

        allEmployeeWorkScheduleDetails[inputEmployeeId] = { weekOffAndHolidayDetails, employeeHolidayDetails, totalBusinessWorkingDays ,workScheduleToSend};
      }
      return allEmployeeWorkScheduleDetails;
  } catch (error) {
      console.error('Error in fetchEmployeeWeekOffAndHolidayDetails', error,  employeeId,startDate,endDate);
      throw error;
  }
};

/**
 * Fetches leave details for specified employees within a date range.
 *
 * @param {Object} organizationDbConnection - The database connection object.
 * @param {Array|Number} employeeId - Employee ID(s) to filter the results.
 * @param {String} salaryDate - The start date of the period (inclusive).
 * @param {String} lastSalaryDate - The end date of the period (inclusive).
 * @param {Array} status - An array of approval statuses to filter the results (e.g., ['Approved']).
 * @param {String} [leaveType] - Optional. The leave type to filter by (e.g., 'Unpaid Leave'). If not provided, all leave types are returned.
 * @returns {Promise<Array>} - A promise that resolves to an array of leave details.
 */
async function getAllLeaveDetails(organizationDbConnection, employeeId, salaryDate, lastSalaryDate, status, leaveType = undefined) {
  try {
      let leaveDetailsQuery = organizationDbConnection(ehrTables.empLeaves + " as L")
          .innerJoin(ehrTables.leaveTypes + " as LT", "LT.LeaveType_Id", "L.LeaveType_Id")
          .select('L.*', 'LT.Leave_Name', 'LT.Leave_Calculation_Days', 'LT.Leave_Type')
          .whereIn('L.Approval_Status', status)
          .whereIn('L.Employee_Id', employeeId)
          .where(function() {
              this.where(function() {
                  // Both start and end outside the date range but the range dates are in between
                  this.where('L.Start_Date', '<', salaryDate)
                      .andWhere('L.End_Date', '>', lastSalaryDate);
              })
              .orWhere(function() {
                  // Both start and end inside the date range
                  this.whereBetween('L.Start_Date', [salaryDate, lastSalaryDate])
                      .andWhereBetween('L.End_Date', [salaryDate, lastSalaryDate]);
              })
              .orWhere(function() {
                  // Start outside and end inside the date range
                  this.where('L.Start_Date', '<', salaryDate)
                      .andWhere('L.End_Date', '>=', salaryDate)
                      .andWhere('L.End_Date', '<=', lastSalaryDate);
              })
              .orWhere(function() {
                  // Start inside and end outside the date range
                  this.where('L.Start_Date', '>=', salaryDate)
                      .andWhere('L.Start_Date', '<=', lastSalaryDate)
                      .andWhere('L.End_Date', '>', lastSalaryDate);
              });
          });

      // Add leave type filter if provided
      if (leaveType !== undefined && leaveType !== null && leaveType !== '') {
          leaveDetailsQuery = leaveDetailsQuery.where('LT.Leave_Type', leaveType);
      }

      leaveDetailsQuery = leaveDetailsQuery
          .orderBy('L.Employee_Id', 'ASC')
          .orderBy('L.Start_Date', 'ASC');

      const leaveDetails = await leaveDetailsQuery;
      return leaveDetails;
  } catch (err) {
      console.error('Error in getAllLeaveDetails', err);
      return [];
  }
}

async function getCompensatoryOffDetails(organizationDbConnection,employeeId, salaryDate, lastSalaryDate, status) {
  try {
      let compensatoryOffDetails = await organizationDbConnection(ehrTables.compensatoryOff+ " as CO")
          .distinct()
          .select('CO.*', 'COB.Worked_Date')
          .leftJoin(ehrTables.compensatoryOffBalance + " as COB", "COB.Comp_Off_Balance_Id", "CO.Comp_Off_Balance_Id")
          .where('CO.Compensatory_Date', '>=', salaryDate)
          .where('CO.Compensatory_Date', '<=', lastSalaryDate)
          .whereIn('CO.Employee_Id', employeeId)
          .whereIn('CO.Approval_Status', status)
          .orderBy(['CO.Employee_Id', 'CO.Compensatory_Date']);        
          return compensatoryOffDetails;
  } catch (err) {
      console.error('Error in getCompensatoryOffDetails', err);
      return [];
  }
}

async function getshortTimeOffDetails(organizationDbConnection,employeeId, salaryDate, lastSalaryDate, status) {
  try {
      let compensatoryOffDetails = await organizationDbConnection(ehrTables.shortTimeOff)
          .distinct()
          .select('*')
          .where('Short_Time_Off_Date', '>=', salaryDate)
          .where('Short_Time_Off_Date', '<=', lastSalaryDate)
          .whereIn('Employee_Id', employeeId)
          .whereIn('Approval_Status', status)
          .orderBy(['Employee_Id', 'Short_Time_Off_Date']);
          return compensatoryOffDetails;
  } catch (err) {
      console.error('Error in getCompensatoryOffDetails', err);
      return [];
  }
}

/**
 * Fetches attendance details for a specified range of dates and employee IDs.
 *
 * @param {Object} organizationDbConnection - The database connection object.
 * @param {Array} employeeIds - An array of employee IDs to filter the results.
 * @param {String} salaryDate - The start date of the salary period (inclusive).
 * @param {String} lastSalaryDate - The end date of the salary period (inclusive).
 * @param {Array} status - An array of approval statuses to filter the results.
 * @returns {Promise<Array>} - A promise that resolves to an array of attendance details.
 */
async function getAttendanceDetails(organizationDbConnection,employeeIds,salaryDate,lastSalaryDate,status)
{
    try 
    {
    let attendanceDetails = await organizationDbConnection(ehrTables.empAttendace)
    .distinct()
    .select('*')
    .where('Attendance_Date', '>=', salaryDate)
    .where('Attendance_Date', '<=', lastSalaryDate)
    .whereIn('Employee_Id', employeeIds)
    .whereIn('Approval_Status',status)
    .orderBy(['Employee_Id', 'Attendance_Date','PunchIn_Date','PunchIn_Time']);//sorted in asc order
    return attendanceDetails;
    } catch (err) {
        console.error('Error in getAttendanceDetails', err);
        return [];
    }
}

//Function to get the early checkout count based on the employee id, attendance date and early checkout value
async function getAttendanceSummary(organizationDbConnection,employeeIds,startDate,endDate) {
  try {
      const summaryDetails = await organizationDbConnection(ehrTables.employeeAttendanceSummary)
          .select('*')
          .whereIn('Employee_Id', employeeIds)
          .where('Attendance_Date', '>=', startDate)
          .where('Attendance_Date', '<=', endDate)
          .orderBy('Employee_Id', 'asc');
      return summaryDetails;
  } catch (error) {
      console.error('Error in getAttendanceSummary function main catch block', error);
      return [];
  }
}

//Function to form to work schedule time range based on the period(First Half, Second Half, First Quarter, Second Quarter, Third Quarter and Fourth Quarter)
function generateTimeOffHours(workSchedule, period) {
  try {
      let timeOffDetail;
      
      switch (period) {
        case 'Full Day':
            timeOffDetail = {
                startDateTime: workSchedule.Regular_From,
                endDateTime: workSchedule.Regular_To
            };
            break;
          case 'First Half':
              timeOffDetail = {
                  startDateTime: workSchedule.Regular_From,
                  endDateTime: workSchedule.Lunch_Break_From
              };
              break;

          case 'Second Half':
              timeOffDetail = {
                  startDateTime: workSchedule.Lunch_Break_To,
                  endDateTime: workSchedule.Regular_To
              };
              break;

          case 'First Quarter':
              timeOffDetail = {
                  startDateTime: workSchedule.Regular_From,
                  endDateTime: workSchedule.First_Half_Break_From
              };
              break;

          case 'Second Quarter':
              timeOffDetail = {
                  startDateTime: workSchedule.First_Half_Break_To,
                  endDateTime: workSchedule.Lunch_Break_From
              };
              break;

          case 'Third Quarter':
              timeOffDetail = {
                  startDateTime: workSchedule.Lunch_Break_To,
                  endDateTime: workSchedule.Second_Half_Break_From
              };
              break;

          case 'Fourth Quarter':
              timeOffDetail = {
                  startDateTime: workSchedule.Second_Half_Break_To,
                  endDateTime: workSchedule.Regular_To
              };
              break;
      }
      return timeOffDetail;   
  }catch(error){
      console.log('Error in generateTimeOffHours function main catch block.', error);
      throw error;
  }
}

function generateTimeOffHoursFromWorkSchedule(workSchedule, period) {
  try {
      let timeOffDetail;
      if(period){
        let keyName = period.replace(" ", "_");
        timeOffDetail = {
          startDateTime: workSchedule[keyName + '_From'],
          endDateTime: workSchedule[keyName + '_To']
        };
      }else{
        timeOffDetail = {
          startDateTime: workSchedule['Regular_From'],
          endDateTime: workSchedule['Regular_To']
        };
      }
      return timeOffDetail;   
  }catch(error){
      console.log('Error in generateTimeOffHoursFromWorkSchedule function main catch block.', error);
      throw error;
  }
}

async function checkIfShiftScheduled(workScheduleArray) {
  try {
    const totalDays = workScheduleArray.length;
    const scheduledDays = workScheduleArray.filter(day => day.isShiftScheduled === 1).length;

    if (scheduledDays !== totalDays) {
      return 'shiftnotscheduled';
    }

    return '';
  } catch (error) {
    console.error('Error in checkIfShiftScheduled:', error);
    throw error;
  }
}

async function getEmployeesWorkScheduleDetails(db, args) {
  try {
    const allEmployeeWorkDetails = []

    const {
      allEmployeeDetails,
      allEmployeeShiftDetails,
      allWorkScheduleDetails,
      employeeHolidayDetails,
      startDate,
      endDate
    } = args || {}

    // Validate input arguments
    if (!Array.isArray(allEmployeeDetails) || allEmployeeDetails.length === 0) {
      console.warn('Invalid or empty allEmployeeDetails array')
      return allEmployeeWorkDetails
    }

    const startMoment = startDate ? moment(startDate) : null
    const endMoment = endDate ? moment(endDate) : null

    if (
      !startMoment ||
      !endMoment ||
      !startMoment.isValid() ||
      !endMoment.isValid()
    ) {
      console.warn('Invalid or missing startDate or endDate')
      return allEmployeeWorkDetails
    }

    const rosterManagementSettings =
      await getRosterManagementSettings(db)

    // Process each employee's work schedule
    for (const employeeDetails of allEmployeeDetails) {
      const employeeId = employeeDetails?.Employee_Id || null
      const workScheduleId = employeeDetails?.WorkSchedule_Id || null

      if (!employeeId || !workScheduleId) {
        console.warn('Missing Employee_Id or WorkSchedule_Id for an employee')
        continue
      }

      const employeeShiftDetails =
        employeeDetails?.Work_Schedule?.toLowerCase() === 'shift roster'
          ? allEmployeeShiftDetails?.[employeeId] || null
          : null

      const employeeWorkScheduleDetails =
        employeeDetails?.Work_Schedule?.toLowerCase() !== 'shift roster'
          ? allWorkScheduleDetails?.[workScheduleId] || null
          : null

      const employeeHolidayResult = employeeHolidayDetails?.[employeeId] || null

      let currentDate = startMoment.clone()

      // Iterate over the date range
      while (currentDate.isSameOrBefore(endMoment)) {
        let workScheduleDetails = null

        try {
          if (
            employeeDetails?.Work_Schedule?.toLowerCase() === 'shift roster'
          ) {
            const workSchedule =
              await getWorkScheduleDetailsByDateForShiftRosterEmployees(
                currentDate.format('YYYY-MM-DD'),
                employeeShiftDetails,
                allWorkScheduleDetails,
                rosterManagementSettings
              )

            if (workSchedule && Object.keys(workSchedule).length > 0) {
              const checkWorkingDayResult =
                await checkWorkingDay(
                  currentDate.format('YYYY-MM-DD'),
                  employeeHolidayResult,
                  workSchedule
                )
              workScheduleDetails =
                checkWorkingDayResult?.Work_Schedule_Details || null
            }
          } else {
            const workSchedule =
              employeeWorkScheduleDetails?.[currentDate.format('YYYY-MM-DD')] ||
              null

            if (workSchedule) {
              const checkWorkingDayResult =
                await checkWorkingDay(
                  currentDate.format('YYYY-MM-DD'),
                  employeeHolidayResult,
                  workSchedule
                )
              workScheduleDetails =
                checkWorkingDayResult?.Work_Schedule_Details || null
            }
          }

          allEmployeeWorkDetails.push({
            Employee_Id: employeeId,
            workScheduleDate: currentDate.format('YYYY-MM-DD'),
            workScheduleDetails: workScheduleDetails
          })
        } catch (innerError) {
          console.error(
            `Error processing work schedule for Employee_Id: ${employeeId} on ${currentDate.format(
              'YYYY-MM-DD'
            )}`,
            innerError
          )
          throw innerError;
        }

        currentDate.add(1, 'days')
      }
    }

    return allEmployeeWorkDetails
  } catch (error) {
    console.error(
      'Error in the getEmployeesWorkScheduleDetails function main catch block.',
      error
    )
    throw error
  }
}

async function prepareAttendanceInputs(
  organizationDbConnection,
  employeeId,
  startDate,
  endDate,
  sendindivudual=false
) {
  try {
    // Fetch all work schedule details
    const allWorkScheduleDetails =
      await getAllWorkScheduleDetails(
        organizationDbConnection,
        startDate,
        endDate
      )
    // Fetch employee details for the given employee ID
    const employeeIdArray = Array.isArray(employeeId) ? employeeId : [employeeId];
    const employeeDetails =
      await getEmployeeDetails(
        organizationDbConnection,
        employeeIdArray,
        startDate,
        endDate
      )
    const allEmployeeShiftDetails =
      await getAllEmployeeShiftDetails(
        organizationDbConnection,
        employeeDetails,
        startDate,
        endDate
      )
    // Fetch all holiday details
    const employeeHolidayDetails =
      await getAllHolidayDates(
        organizationDbConnection,
        startDate,
        endDate
      )
    // Fetch roster management settings for shift-roster logic
    const rosterManagementSettings =
      await getRosterManagementSettings(organizationDbConnection)

    const validateAttendanceInputs = {
      allEmployeeDetails: employeeDetails,
      allEmployeeShiftDetails,
      allWorkScheduleDetails,
      employeeHolidayDetails,
      startDate,
      endDate
    }

    let employeesWorkScheduleDetails = await getEmployeesWorkScheduleDetails(
      organizationDbConnection,
      validateAttendanceInputs
    )
    if(sendindivudual){
        return {
      employeeDetails,
      allWorkScheduleDetails,
      allEmployeeShiftDetails,
      allHolidayDetails:employeeHolidayDetails,
      rosterManagementSettings,
      employeesWorkScheduleDetails:employeesWorkScheduleDetails

      }
    }
    return employeesWorkScheduleDetails
  } catch (error) {
    console.error('Error in prepareValidateLeaveInputs:', error)
    throw error
  }
}
/**
 * Get business working days for an employee (matches PHP's getBusinessWorkingDays)
 * This implements the same logic as PHP to calculate working days based on employee configuration
 *
 * @param {String} salaryDate - Salary start date
 * @param {String} lastSalaryDate - Salary end date
 * @param {Number} employeeId - Employee ID
 * @param {Number} totalWorkingDays - Total working days (optional)
 * @param {Number} leaveCalculationDays - Leave calculation days flag (optional)
 * @param {String} formName - Form name (optional)
 * @param {String} compOffCalculationMethod - Comp off calculation method (optional)
 * @param {Object} employeeWorkScheduleShiftDetails - Work schedule data structure
 *
 * @returns {Object} Result object with structure:
 *   - {Number|null} days - Business working days count, or null if there's an error
 *   - {String|null} error - Error code/message if applicable, or null if successful
 *
 * @example
 * // Successful calculation
 * const result = await getBusinessWorkingDays(...);
 * if (result.error) {
 *   console.error('Error:', result.error);
 * } else {
 *   console.log('Working days:', result.days);
 * }
 */
async function getBusinessWorkingDays(salaryDate, lastSalaryDate, employeeId, totalWorkingDays = null, leaveCalculationDays = null, formName = null, compOffCalculationMethod = null, employeeWorkScheduleShiftDetails = null) {
  let workingDays = 0;

  // Guard against null/undefined employeeWorkScheduleShiftDetails
  if (!employeeWorkScheduleShiftDetails) {
    console.log(`No employeeWorkScheduleShiftDetails provided for Employee ${employeeId}`);
    return { days: 0, error: null };
  }

  // Organize employee details by Employee_Id
  const employeeDetailsArray = employeeWorkScheduleShiftDetails.employeeDetails || [];
  const employeeDetail = employeeDetailsArray.find(emp => emp.Employee_Id === employeeId);

  if (!employeeDetail) {
    console.log(`No employee details found for Employee ${employeeId}`);
    return { days: 0, error: null };
  }

  // Adjust salary dates if employee's dates are more restrictive
  let adjustedSalaryDate = salaryDate;
  let adjustedLastSalaryDate = lastSalaryDate;

  if (employeeDetail.Salary_Date && moment(employeeDetail.Salary_Date).isAfter(moment(salaryDate)) && totalWorkingDays === null) {
    adjustedSalaryDate = employeeDetail.Salary_Date;
  }

  if (employeeDetail.Last_Salary_Date && moment(employeeDetail.Last_Salary_Date).isBefore(moment(lastSalaryDate)) && totalWorkingDays === null) {
    adjustedLastSalaryDate = employeeDetail.Last_Salary_Date;
  }

  // Handle comp-off calculation method
  let salaryCalcDays = employeeDetail.Salary_Calc_Days;
  let fixedDays = employeeDetail.Fixed_Days;

  if (compOffCalculationMethod === 'Yes') {
    salaryCalcDays = employeeDetail.Comp_Off_Days;
    fixedDays = employeeDetail.Comp_Off_Fixed_Days;
  }

  // Normalize values to numbers to prevent type coercion bugs
  const normalizedSalaryCalcDays = Number(salaryCalcDays);
  const normalizedLeaveCalculationDays = Number(leaveCalculationDays);
  const normalizedFixedDays = Number(fixedDays);

  // Three calculation modes based on Salary_Calc_Days
  /* Salary_Calc_Days flag value reference:
   * 0 - Business Working Days
   * 1 - All Days of Salary Month
   * 2 - Average Days in a Month
   * 3 - Fixed Days of Salary Month
   */

  console.log(normalizedSalaryCalcDays,"normalizedSalaryCalcDays");
  console.log(normalizedLeaveCalculationDays,"normalizedLeaveCalculationDays");
  console.log(adjustedSalaryDate,"adjustedSalaryDate");
  console.log(adjustedLastSalaryDate,"adjustedLastSalaryDate");
  if (normalizedSalaryCalcDays === 0 || normalizedLeaveCalculationDays === 1) {
    // Mode 0: Calculate based on actual work schedule (sum Business_Working_Days)
    const currentDate = moment(adjustedSalaryDate);
    const endDate = moment(adjustedLastSalaryDate);

    while (currentDate.isSameOrBefore(endDate)) {
      const dateStr = currentDate.format('YYYY-MM-DD');
      const workScheduleDetails = await getWorkScheduleDetailsByDate(employeeId, dateStr, employeeWorkScheduleShiftDetails);
      if (workScheduleDetails && workScheduleDetails.Business_Working_Days >= 0) {
        workingDays += parseFloat(workScheduleDetails.Business_Working_Days);
      } else if (formName && formName.toLowerCase() === AppConstant.leavesFormName.toLowerCase() && employeeDetail.Work_Schedule && employeeDetail.Work_Schedule.toLowerCase() === 'shift roster') {
        // Shift not scheduled - return error for shift roster employees when checking leaves
        return { days: null, error: 'shiftnotscheduled' };
      }

      currentDate.add(1, 'days');
    }
  } else if (normalizedSalaryCalcDays === 1 || normalizedSalaryCalcDays === 2) {
    // Mode 1 & 2: Calendar days (date difference + 1)
    const start = moment(adjustedSalaryDate);
    const end = moment(adjustedLastSalaryDate);
    const diffDays = end.diff(start, 'days');
    workingDays = diffDays + 1;
  } else if (normalizedSalaryCalcDays === 3) {
    // Mode 3: Fixed days (minimum of calculated days or configured fixed days)
    const start = moment(adjustedSalaryDate);
    const end = moment(adjustedLastSalaryDate);
    const diffDays = end.diff(start, 'days');
    const calculatedDays = diffDays + 1;
    workingDays = Math.min(calculatedDays, normalizedFixedDays || calculatedDays);
  }

  return { days: workingDays, error: null };
}

/**
 * Get work schedule details by date for an employee (matches PHP's getWorkScheduleDetailsByDate)
 * @param {Number} employeeId - Employee ID
 * @param {String} startDate - Date to check
 * @param {Object} employeeWorkScheduleShiftDetails - Work schedule data structure
 * @returns {Object} - Work schedule details for the date
 */
async function getWorkScheduleDetailsByDate(employeeId, startDate, employeeWorkScheduleShiftDetails) {
  // Validate and default employeeWorkScheduleShiftDetails to prevent undefined access
  if (!employeeWorkScheduleShiftDetails || typeof employeeWorkScheduleShiftDetails !== 'object') {
    console.log(`Invalid employeeWorkScheduleShiftDetails for Employee ${employeeId}`);
    return null;
  }

  // Default all nested properties to safe values
  const employeeDetailsArray = employeeWorkScheduleShiftDetails.employeeDetails || [];
  const allWorkScheduleDetails = employeeWorkScheduleShiftDetails.allWorkScheduleDetails || {};
  const allEmployeeShiftDetails = employeeWorkScheduleShiftDetails.allEmployeeShiftDetails || {};
  const allHolidayDetails = employeeWorkScheduleShiftDetails.allHolidayDetails || {};
  const rosterManagementSettings = employeeWorkScheduleShiftDetails.rosterManagementSettings || {};

  const employeeDetail = employeeDetailsArray.find(emp => emp.Employee_Id === employeeId);

  if (!employeeDetail) {
    return null;
  }

  const workScheduleId = employeeDetail.WorkSchedule_Id;
  const employeeHolidayDetails = allHolidayDetails[employeeId] || null;

  // Case-insensitive shift roster check
  if ((employeeDetail.Work_Schedule || '').toLowerCase() === 'shift roster') {
    // For shift roster employees - use safe lookup
    const employeeShiftDetails = allEmployeeShiftDetails[employeeId] || null;
    const workSchedule = await getWorkScheduleDetailsByDateForShiftRosterEmployees(
      startDate,
      employeeShiftDetails,
      allWorkScheduleDetails,
      rosterManagementSettings
    );

    if (workSchedule && Object.keys(workSchedule).length > 0) {
      const checkWorkingDayResult = await checkWorkingDay(
        startDate,
        employeeHolidayDetails,
        workSchedule
      );
      return checkWorkingDayResult.Work_Schedule_Details;
    }
  } else {
    // For regular work schedule employees - use safe lookup
    const employeeWorkScheduleDetails = allWorkScheduleDetails[workScheduleId] || null;
    const workSchedule = employeeWorkScheduleDetails ? employeeWorkScheduleDetails[startDate] : null;

    if (workSchedule) {
      const checkWorkingDayResult = await checkWorkingDay(
        startDate,
        employeeHolidayDetails,
        workSchedule
      );
      return checkWorkingDayResult.Work_Schedule_Details;
    }
  }

  return null;
}

/**
 * Get unpaid leave days for multiple employees in batch
 * @param {Object} db - Database connection
 * @param {Array} employeeIds - Array of employee IDs
 * @param {String} salaryDate - Salary start date
 * @param {String} lastSalaryDate - Salary end date
 * @param {String} payslipMonth - Payslip month in format "M,YYYY"
 * @returns {Promise<Map>} - Map with employeeId as key and unpaid leave days as value
 */
async function getUnpaidLeaveDaysForEmployees(db, employeeIds, salaryDate, lastSalaryDate, payslipMonth) {
  try {
    const resultMap = new Map();

    // Initialize all employees with 0
    employeeIds.forEach(empId => resultMap.set(empId, 0));
    const leaveDetails = await getAllLeaveDetails(
      db,
      employeeIds,
      salaryDate,
      lastSalaryDate,
      ['Approved'],
      'Unpaid Leave'
    );

    // Sum up Total_Days per employee from leave details
    leaveDetails.forEach(leave => {
      const currentTotal = resultMap.get(leave.Employee_Id) || 0;
      resultMap.set(leave.Employee_Id, currentTotal + parseFloat(leave.Total_Days || 0));
    });

    // Get unpaid leave override days for all employees in batch (single query)
    const overrideDays = await getUnpaidLeaveOverrideDaysForEmployees(
      db,
      employeeIds,
      payslipMonth
    );

    // Add override days to the result
    overrideDays.forEach((days, employeeId) => {
      const currentTotal = resultMap.get(employeeId) || 0;
      resultMap.set(employeeId, currentTotal + days);
    });

    return resultMap;
  } catch (error) {
    console.error('Error in getUnpaidLeaveDaysForEmployees:', error);
    // Return map with 0 for all employees on error
    const resultMap = new Map();
    employeeIds.forEach(empId => resultMap.set(empId, 0));
    return resultMap;
  }
}

/**
 * Get unpaid leave override days for multiple employees in batch
 * @param {Object} db - Database connection
 * @param {Array} employeeIds - Array of employee IDs
 * @param {String} payslipMonth - Payslip month in format "M,YYYY"
 * @returns {Promise<Map>} - Map with employeeId as key and override days as value
 */
async function getUnpaidLeaveOverrideDaysForEmployees(db, employeeIds, payslipMonth) {
  try {
    const resultMap = new Map();

    // Initialize all employees with 0
    employeeIds.forEach(empId => resultMap.set(empId, 0));

    // Get unpaid leave override days for all employees in batch (single query)
    const overrideData = await db(ehrTables.unpaidLeaveOverride + ' as ULO')
      .innerJoin(ehrTables.empLeaves + ' as EL', 'ULO.Unpaid_Leave_Override_Id', 'EL.Leave_Id')
      .select(
        'EL.Employee_Id',
        db.raw('SUM(ULO.Total_Leave_Days) as Total_Override_Days')
      )
      .where('ULO.Unpaid_Leave_Override', 'Leave')
      .whereIn('EL.Employee_Id', employeeIds)
      .where('ULO.Payslip_Month', payslipMonth)
      .groupBy('EL.Employee_Id');

    // Populate the result map
    overrideData.forEach(row => {
      resultMap.set(row.Employee_Id, parseFloat(row.Total_Override_Days || 0));
    });

    return resultMap;
  } catch (error) {
    console.error('Error in getUnpaidLeaveOverrideDaysForEmployees:', error);
    // Return map with 0 for all employees on error
    const resultMap = new Map();
    employeeIds.forEach(empId => resultMap.set(empId, 0));
    return resultMap;
  }
}

module.exports = {
  calculateWorkScheduleWeekOffDates,
  weekOfMonth,
  getLunchBreakHours,
  getAllWorkScheduleDetails,
  getEmployeeDetails,
  getAllEmployeeShiftDetails,
  getAllHolidayDates,
  checkWorkingDay,
  getRosterManagementSettings,
  getWorkScheduleDetailsByDateForShiftRosterEmployees,
  getAllLeaveDetails,
  getCompensatoryOffDetails,
  getshortTimeOffDetails,
  getAttendanceDetails,
  getAttendanceSummary,
  generateTimeOffHours,
  generateTimeOffHoursFromWorkSchedule,
  fetchEmployeeWeekOffAndHolidayDetails,
  checkIfShiftScheduled,
  processEmployeeSchedule,
  getEmployeesWorkScheduleDetails,
  prepareAttendanceInputs,
  getBusinessWorkingDays,
  getWorkScheduleDetailsByDate,
  getUnpaidLeaveDaysForEmployees,
  getUnpaidLeaveOverrideDaysForEmployees
}; 
