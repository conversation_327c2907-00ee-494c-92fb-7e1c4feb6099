// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { getEmployeeComponentValuesInBulk, checkForRuntimeComponents } = require('../common/commonfunctions');
const moment = require('moment-timezone');

// resolver definition
const resolvers = {
    Query: {
        // function to list one time earnings by payout or clawback month or for payroll prerequisite
        listBenefitAndDeductionByMonth: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listBenefitAndDeductionByMonth function');
                const loggedInEmpId = context.logInEmpId;
                const { formId, month, year, action, employeeIds,payOutPeriod } = args;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // Validate action
                if (!['payout', 'clawback', 'payrollprereq'].includes(action.toLowerCase())) {
                    throw 'IVE0779'; // Invalid action type
                }

                // get one time earnings data filtered by month
                const oneTimeEarningsData = await getOneTimeEarningsByMonth(
                    organizationDbConnection,
                    month,
                    year,
                    action.toLowerCase(),
                    employeeIds,
                    payOutPeriod,
                    context
                );

                // return response
                return {
                    errorCode: '',
                    message: `One time earnings for ${action} month ${month}/${year} listed successfully.`,
                    success: true,
                    oneTimeEarnings: JSON.stringify(oneTimeEarningsData)
                };
            }
            catch (mainCatchError) {
                console.log('Error in listBenefitAndDeductionByMonth function main catch block', mainCatchError);

                if (mainCatchError === 'IVE0774') {
                    const errResult = commonLib.func.getError('', mainCatchError);
                    throw new ApolloError(errResult.message1, mainCatchError);
                }

                const errResult = commonLib.func.getError(mainCatchError, 'PST0046');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    organizationDbConnection.destroy();
                }
            }
        }
    }
};

// function to get one time earnings filtered by payout or clawback month or for payroll prerequisite
async function getOneTimeEarningsByMonth(organizationDbConnection, month, year, action, employeeIds, payOutPeriod, context) {
    try {
        return await organizationDbConnection.transaction(async (trx) => {
            // Build the M,YYYY format for filtering
            const targetMonth = `${month}, ${year}`;
            // Build query for one time earnings with JOIN to get type config
            let oneTimeEarningsQuery = organizationDbConnection('employee_one_time_earnings as EOTE')
                .select(
                    // Employee table fields (stored)
                    'EOTE.One_Time_Earning_Id AS Description',
                    'EOTE.Employee_Id',
                    'EOTE.Amount',
                    'EOTE.Calculated_Amount',
                    'EOTE.Clawback_Amount',
                    'EOTE.Calculated_Clawback_Amount',
                    'EOTE.Custom_Formula',
                    'EOTE.Clawback_Formula',
                    'EOTE.Payout_Month',
                    'EOTE.Payout_End_Month',
                    // Adhoc allowance type title
                    'AAT.Name_In_Payslip',
                    'AAT.Tax_Inclusion',
                    organizationDbConnection.raw('GROUP_CONCAT(AABA.Form_Id) as BenefitForms')
                )
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'EOTE.Employee_Id')
                .join(ehrTables.onetimeEarningTypes + ' as AAT', 'AAT.One_Time_Earning_Type_Id', 'EOTE.One_Time_Earning_Type_Id')
                .leftJoin(ehrTables.adhocAllowanceBenefitAssociation + ' as AABA', 'AABA.Adhoc_Allowance_Id', 'AAT.One_Time_Earning_Type_Id')
                .groupBy(
                    'EOTE.One_Time_Earning_Id',
                    'EOTE.Employee_Id',
                    'EOTE.Payout_Month',
                    'EOTE.Amount',
                    'EOTE.Calculated_Amount',
                    'EOTE.Clawback_Amount',
                    'EOTE.Calculated_Clawback_Amount',
                    'EOTE.Custom_Formula',
                    'EOTE.Clawback_Formula',
                    'EOTE.Payout_End_Month',
                    'AAT.Name_In_Payslip',
                    'AAT.Tax_Inclusion'
                )
                .transacting(trx);

            // Filter by payout month or clawback month based on action (M,YYYY format)
            if (action === 'payout') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where(function() {
                        // Case 1: Payout_End_Month is null - show all months >= targetMonth
                        this.where(function() {
                            this.whereRaw(`STR_TO_DATE(CONCAT('01, ', EOTE.Payout_Month), '%d, %m, %Y') >= STR_TO_DATE(CONCAT('01, ', ?), '%d, %m, %Y')`, [targetMonth])
                                .whereNull('EOTE.Payout_End_Month');
                        })
                        // Case 2: Payout_End_Month is not null - target month should be between Payout_Month and Payout_End_Month
                        // Convert "M, YYYY" format to YYYY-MM-01 for proper date comparison
                        .orWhere(function() {
                            this.whereRaw(`STR_TO_DATE(CONCAT('01, ', EOTE.Payout_Month), '%d, %m, %Y') <= STR_TO_DATE(CONCAT('01, ', ?), '%d, %m, %Y')`, [targetMonth])
                                .whereRaw(`STR_TO_DATE(CONCAT('01, ', EOTE.Payout_End_Month), '%d, %m, %Y') >= STR_TO_DATE(CONCAT('01, ', ?), '%d, %m, %Y')`, [targetMonth])
                                .whereNotNull('EOTE.Payout_End_Month');
                        });
                    });
            } else if (action === 'clawback') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where('EOTE.Commitment_End_Month', targetMonth)
                    .whereNotNull('EOTE.Commitment_End_Month');
            } else if (action === 'payrollprereq') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where(function() {
                        // Case 1: Payout_End_Month is null - show all months >= targetMonth
                        this.where(function() {
                            this.whereRaw(`STR_TO_DATE(CONCAT('01, ', EOTE.Payout_Month), '%d, %m, %Y') >= STR_TO_DATE(CONCAT('01, ', ?), '%d, %m, %Y')`, [targetMonth])
                                .whereNull('EOTE.Payout_End_Month');
                        })
                        // Case 2: Payout_End_Month is not null - target month should be between Payout_Month and Payout_End_Month
                        // Convert "M, YYYY" format to YYYY-MM-01 for proper date comparison
                        .orWhere(function() {
                            this.whereRaw(`STR_TO_DATE(CONCAT('01, ', EOTE.Payout_Month), '%d, %m, %Y') <= STR_TO_DATE(CONCAT('01, ', ?), '%d, %m, %Y')`, [targetMonth])
                                .whereRaw(`STR_TO_DATE(CONCAT('01, ', EOTE.Payout_End_Month), '%d, %m, %Y') >= STR_TO_DATE(CONCAT('01, ', ?), '%d, %m, %Y')`, [targetMonth])
                                .whereNotNull('EOTE.Payout_End_Month');
                        });
                    });
            }
            
            if (action === 'payout' || action === 'clawback') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where('EOTE.Approval_Status', 'Approved');
            } else if (action === 'payrollprereq') {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .whereIn('EOTE.Approval_Status', ['Applied', 'Cancel Applied']);
            }

            if (payOutPeriod) {
                oneTimeEarningsQuery = oneTimeEarningsQuery
                    .where('EOTE.Payout_Period', payOutPeriod);
            }

            // Filter by employee if provided multiple employees
            if (employeeIds && Array.isArray(employeeIds) && employeeIds.length > 0) {
                oneTimeEarningsQuery = oneTimeEarningsQuery.whereIn('EOTE.Employee_Id', employeeIds);
            }
            
            const oneTimeEarnings = await oneTimeEarningsQuery;

            const onTimeEarningIds = oneTimeEarnings.map(e => e.Description);
            if(onTimeEarningIds.length===0){
                return [];
            }
            
            // Filter earnings that need calculation based on action type
            let earningsNeedingCalculation = [];

            if (action === 'payout') {
                // For payout action: fetch components only for earnings needing Amount calculation
                earningsNeedingCalculation = oneTimeEarnings.filter(
                    earning => earning.Custom_Formula && checkForRuntimeComponents(earning.Custom_Formula).length > 0
                );
            } else if (action === 'clawback') {
                // For clawback action: fetch components only for earnings needing Clawback_Amount calculation
                earningsNeedingCalculation = oneTimeEarnings.filter(
                    earning => earning.Clawback_Formula && checkForRuntimeComponents(earning.Clawback_Formula).length > 0
                );
            }
            // Batch fetch all components only for employees with earnings that need calculation
            const uniqueEmployeeIds = [...new Set(earningsNeedingCalculation.map(e => e.Employee_Id))];
            let allComponentValuesMap = new Map();
            
            if (uniqueEmployeeIds.length > 0) {
                try {
                    allComponentValuesMap = await getEmployeeComponentValuesInBulk(
                        organizationDbConnection,
                        uniqueEmployeeIds,
                        targetMonth,
                        context
                    );
                } catch (err) {
                    console.error('Error fetching component values in bulk:', err);
                    allComponentValuesMap = new Map();
                }
            }

            let oneTimeEarningsFormatted = [];
            const currentDate = moment().utc();
            const calculatedEarnings = [];

            for (const earning of oneTimeEarnings) {

                if (action === 'payout' && earning.Payout_End_Month && hasPayoutEnded(earning.Payout_End_Month, month, year)) {
                    // Skip earnings where payout period has ended
                    continue;
                }

                let finalAmount = earning.Calculated_Amount;
                let finalClawbackAmount = earning.Calculated_Clawback_Amount;
                let hasPayoutRuntimeComponents = false;
                let hasClawbackRuntimeComponents = false;
                if(action === 'payout' && earning.Custom_Formula) {
                   const runtimeComps = checkForRuntimeComponents(earning.Custom_Formula);
                    hasPayoutRuntimeComponents = runtimeComps.length > 0;
                }
        
                if (action === 'payout' && hasPayoutRuntimeComponents) {
                    try {
                        // Get all component values (regular + dynamic) for this employee
                        const allComponentValues = allComponentValuesMap.get(earning.Employee_Id) || {};

                        // Evaluate formula with merged component values
                        const formulaEvalResult = await commonLib.evaluateFormula.evaluateFormula(earning.Custom_Formula,
                            allComponentValues,
                            organizationDbConnection,
                            false);
                        if (formulaEvalResult.success) {
                            finalAmount = formulaEvalResult.result;
                        } else {
                            console.error(`Error evaluating formula for earning ${earning.Description}:`, formulaEvalResult.error);
                            finalAmount = 0;
                        }
                    } catch (err) {
                        console.error(`Error calculating amount for earning ${earning.Description}:`, err);
                        finalAmount = 0;
                    }
                }
                if(action === 'clawback' && earning.Clawback_Formula) {
                    const runtimeComps = checkForRuntimeComponents(earning.Clawback_Formula);
                    hasClawbackRuntimeComponents = runtimeComps.length > 0;
                }

                // If Clawback_Amount is NULL, calculate using Clawback_Formula (only for clawback action)
                if (action === 'clawback' && hasClawbackRuntimeComponents) {
                    try {
                        // Get all component values (regular + dynamic) for this employee
                        const allComponentValues = allComponentValuesMap.get(earning.Employee_Id) || {};

                        // Evaluate clawback formula with merged component values
                        const clawbackFormulaEvalResult = await commonLib.evaluateFormula.evaluateFormula(
                            earning.Clawback_Formula,
                            allComponentValues,
                            organizationDbConnection,
                            false
                        );

                        if (clawbackFormulaEvalResult.success) {
                            finalClawbackAmount = clawbackFormulaEvalResult.result;
                        } else {
                            console.error(`Error evaluating clawback formula for earning ${earning.Description}:`, clawbackFormulaEvalResult.error);
                            finalClawbackAmount = 0;
                        }
                    } catch (err) {
                        console.error(`Error calculating clawback amount for earning ${earning.Description}:`, err);
                        finalClawbackAmount = 0;
                    }
                }

                calculatedEarnings.push({
                    id: earning.Description,
                    employeeId: earning.Employee_Id,
                    finalAmount,
                    amount: earning.Amount,
                    finalClawbackAmount,
                    clawBackAmount: earning.Clawback_Amount,
                    name: earning.Name_In_Payslip,
                    taxInclusion: earning.Tax_Inclusion,
                    benefitForms: earning.BenefitForms
                });
            }
            if (calculatedEarnings.length > 0) {
                const earningIds = [];
                const updateData = {};

                for (const earning of calculatedEarnings) {
                    earningIds.push(earning.id);
                }
                if (action === 'payout') {
                    const amountCaseValues = [];
                    const calcAmountCaseValues = [];
                    
                    for (const earning of calculatedEarnings) {
                        amountCaseValues.push(earning.id, earning.finalAmount);
                        calcAmountCaseValues.push(earning.id, earning.finalAmount);
                    }

                    let amountCaseSQL = 'CASE WHEN Amount IS NULL THEN CASE';
                    for (let i = 0; i < calculatedEarnings.length; i++) {
                        amountCaseSQL += ' WHEN One_Time_Earning_Id = ? THEN ?';
                    }
                    amountCaseSQL += ' ELSE Amount END ELSE Amount END';
                    
                    updateData.Amount = organizationDbConnection.raw(amountCaseSQL, amountCaseValues);

                    let calcAmountCaseSQL = 'CASE';
                    for (let i = 0; i < calculatedEarnings.length; i++) {
                        calcAmountCaseSQL += ' WHEN One_Time_Earning_Id = ? THEN ?';
                    }
                    calcAmountCaseSQL += ' ELSE Calculated_Amount END';
                    
                    updateData.Calculated_Amount = organizationDbConnection.raw(calcAmountCaseSQL, calcAmountCaseValues);


                } else if (action === 'clawback') {
                    const clawbackCaseValues = [];
                    const calcClawbackCaseValues = [];
                    
                    for (const earning of calculatedEarnings) {
                        clawbackCaseValues.push(earning.id, earning.finalClawbackAmount);
                        calcClawbackCaseValues.push(earning.id, earning.finalClawbackAmount);
                    }
                    let clawbackCaseSQL = 'CASE WHEN Clawback_Amount IS NULL THEN CASE';
                    for (let i = 0; i < calculatedEarnings.length; i++) {
                        clawbackCaseSQL += ' WHEN One_Time_Earning_Id = ? THEN ?';
                    }
                    clawbackCaseSQL += ' ELSE Clawback_Amount END ELSE Clawback_Amount END';
                    
                    updateData.Clawback_Amount = organizationDbConnection.raw(clawbackCaseSQL, clawbackCaseValues);

                    let calcClawbackCaseSQL = 'CASE';
                    for (let i = 0; i < calculatedEarnings.length; i++) {
                        calcClawbackCaseSQL += ' WHEN One_Time_Earning_Id = ? THEN ?';
                    }
                    calcClawbackCaseSQL += ' ELSE Calculated_Clawback_Amount END';
                    
                    updateData.Calculated_Clawback_Amount = organizationDbConnection.raw(calcClawbackCaseSQL, calcClawbackCaseValues);

                }
                if (Object.keys(updateData).length > 0) {
                    await organizationDbConnection('employee_one_time_earnings')
                        .transacting(trx)
                        .update(updateData)
                        .whereIn('One_Time_Earning_Id', earningIds);
                }
            }
            let getUpdateAmountsQuery = organizationDbConnection('employee_one_time_earnings as EOTE')
                .select(
                    'EOTE.One_Time_Earning_Id AS Description',
                    'EOTE.Amount',
                    'EOTE.Clawback_Amount',
                )
                .transacting(trx)
                .whereIn('EOTE.One_Time_Earning_Id', onTimeEarningIds);

            const updatedEarnings = await getUpdateAmountsQuery;

            for (const earning of calculatedEarnings) {
                oneTimeEarningsFormatted.push({
                    EmployeeId: earning.employeeId,
                    Component_Name: "Incentives & Recoveries",
                    Name_In_Payslip: earning.name,
                    Description: earning.id,
                    Tax_Inclusion: earning.taxInclusion,
                    Amount: updatedEarnings.find(e => e.Description === earning.id).Amount,
                    CalculatedAmount: earning.finalAmount,
                    ClawbackAmount: updatedEarnings.find(e => e.Description === earning.id).Clawback_Amount,
                    Calculated_Clawback_Amount: earning.finalClawbackAmount,
                    Benifit_assosiationIds: earning.benefitForms ? earning.benefitForms.split(',').map(id => parseInt(id)) : []
                });
            }
            return oneTimeEarningsFormatted;
        });
    } catch (error) {
        console.log('Error in getOneTimeEarningsByMonth function', error);
        throw error;
    }
}
function hasPayoutEnded(payoutEndMonth, currentMonth, currentYear) {
    if (!payoutEndMonth) {
        // If no end month, payout never ends
        return false;
    }

    try {
        const [endMonth, endYear] = payoutEndMonth.split(',').map(v => {
            const trimmed = v.trim();
            return parseInt(trimmed, 10);
        });

        // If current date is past the end month, payout has ended
        if (currentYear > endYear) return true;
        if (currentYear === endYear && currentMonth > endMonth) return true;

        return false;
    } catch (error) {
        console.error('Error parsing Payout_End_Month:', payoutEndMonth, error);
        return false;
    }
}

module.exports = {
    resolvers
};